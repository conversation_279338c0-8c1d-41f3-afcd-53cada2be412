import pandas as pd
import json
import re
import os
import pickle
from typing import List, Dict
from contextlib import contextmanager
import sys
import torch
from tqdm import tqdm
import numpy as np
import faiss
# Thêm import cho chuẩn hóa văn bản và word segmentation
from underthesea import text_normalize, word_tokenize
from rank_bm25 import BM25Okapi

# Import BGE-M3
try:
    from FlagEmbedding import BGEM3FlagModel
except ImportError:
    print("Warning: FlagEmbedding not found. Please install: pip install FlagEmbedding")
    BGEM3FlagModel = None

@contextmanager
def suppress_stdout_stderr():
    """Context manager để suppress output"""
    with open(os.devnull, "w") as devnull:
        old_stdout = sys.stdout
        old_stderr = sys.stderr
        try:
            sys.stdout = devnull
            sys.stderr = devnull
            yield
        finally:
            sys.stdout = old_stdout
            sys.stderr = old_stderr

def load_data(train_path='train.json', legal_corpus_path='legal_corpus.json'):
    # Đọc file train.json
    with open(train_path, 'r', encoding='utf-8') as f:
        train_data = json.load(f)
    train_df = pd.DataFrame(train_data)

    # Đọc file legal_corpus.json
    with open(legal_corpus_path, 'r', encoding='utf-8') as f:
        legal_corpus = json.load(f)

    # Chuyển đổi legal_corpus thành DataFrame
    legal_articles = []
    for doc in legal_corpus:
        for article in doc['content']:
            legal_articles.append({
                'doc_id': doc['id'],
                'law_id': doc['law_id'],
                'aid': article['aid'],
                'content_Article': article['content_Article']
            })

    legal_df = pd.DataFrame(legal_articles)

    return train_df, legal_df

def find_enumeration_ranges(content):
    """
    Tìm các khoảng văn bản chứa danh sách liệt kê sau dấu ":"
    Trả về list of tuples (start, end) của các khoảng cần bảo vệ
    """
    enumeration_ranges = []
    
    # Pattern 1: Tìm cấu trúc liệt kê sau dấu : với xuống dòng (\n\n1.)
    pattern1 = r':(?:\s*\n){1,3}\s*1\.\s'
    
    # Pattern 2: Tìm cấu trúc liệt kê sau dấu : không xuống dòng (: 1.)
    pattern2 = r':\s*1\.\s'
    
    # Xử lý cả hai pattern
    patterns = [pattern1, pattern2]
    
    for pattern in patterns:
        for match in re.finditer(pattern, content):
            colon_pos = match.start()
            first_item_start = match.end() - 3  # Vị trí của "1. "
            
            # Tìm phần cuối của danh sách liệt kê
            # Tìm số thứ tự cuối cùng trong danh sách
            current_pos = first_item_start
            last_item_end = first_item_start
            item_number = 1
            
            while True:
                # Tìm item tiếp theo
                if pattern == pattern1:  # Có xuống dòng
                    next_item_pattern = rf'(?:\s*\n)+\s*{item_number + 1}\.\s'
                else:  # Không xuống dòng, tìm trong cùng dòng hoặc dòng tiếp theo
                    next_item_pattern = rf';\s*{item_number + 1}\.\s|(?:\s*\n)+\s*{item_number + 1}\.\s'
                
                next_match = re.search(next_item_pattern, content[current_pos:])
                
                if next_match:
                    # Tìm thấy item tiếp theo
                    item_number += 1
                    current_pos += next_match.end()
                    
                    # Tìm kết thúc của item này (trước item tiếp theo hoặc kết thúc văn bản)
                    if pattern == pattern1:
                        next_next_pattern = rf'(?:\s*\n)+\s*{item_number + 1}\.\s'
                    else:
                        next_next_pattern = rf';\s*{item_number + 1}\.\s|(?:\s*\n)+\s*{item_number + 1}\.\s'
                    
                    next_next_match = re.search(next_next_pattern, content[current_pos:])
                    
                    if next_next_match:
                        last_item_end = current_pos + next_next_match.start()
                    else:
                        # Đây là item cuối cùng, tìm kết thúc của nó
                        remaining_text = content[current_pos:]
                        
                        # Tìm dấu kết thúc đoạn văn
                        if pattern == pattern1:
                            end_pattern = r'\n\s*\n\s*(?!\d+\.)'
                        else:
                            end_pattern = r'\.(?:\s*\n\s*\n|\s*$)'
                        
                        end_match = re.search(end_pattern, remaining_text)
                        
                        if end_match:
                            last_item_end = current_pos + end_match.start() + 1  # +1 để bao gồm dấu chấm
                        else:
                            # Nếu không tìm thấy, lấy hết phần còn lại
                            last_item_end = len(content)
                        break
                else:
                    # Không tìm thấy item tiếp theo, kết thúc danh sách
                    remaining_text = content[current_pos:]
                    
                    # Tìm dấu kết thúc đoạn văn
                    if pattern == pattern1:
                        end_pattern = r'\n\s*\n\s*(?!\d+\.)'
                    else:
                        end_pattern = r'\.(?:\s*\n\s*\n|\s*$)'
                    
                    end_match = re.search(end_pattern, remaining_text)
                    
                    if end_match:
                        last_item_end = current_pos + end_match.start() + 1  # +1 để bao gồm dấu chấm
                    else:
                        # Nếu không tìm thấy, lấy hết phần còn lại
                        last_item_end = len(content)
                    break
            
            # Lưu khoảng của toàn bộ cấu trúc liệt kê (từ trước dấu : đến hết danh sách)
            # Tìm điểm bắt đầu của câu chứa dấu :
            text_before_colon = content[:colon_pos]
            sentence_start = max(
                text_before_colon.rfind('\n\n'),
                text_before_colon.rfind('. '),
                0
            )
            if sentence_start > 0 and content[sentence_start:sentence_start+2] == '\n\n':
                sentence_start += 2
            elif sentence_start > 0 and content[sentence_start:sentence_start+2] == '. ':
                sentence_start += 2
                
            enumeration_ranges.append((sentence_start, last_item_end))
    
    # Tìm các chuỗi đánh số phân cấp như 1.1., 1.2., 1.3.
    hierarchical_pattern = r'\b(\d+\.\d+\.)\s'
    hierarchical_matches = list(re.finditer(hierarchical_pattern, content))
    
    if hierarchical_matches:
        # Nhóm các matches theo prefix (1.1, 1.2 cùng group với nhau)
        groups = {}
        for match in hierarchical_matches:
            number = match.group(1)
            prefix = number.split('.')[0]  # Lấy số đầu tiên (1 từ 1.1.)
            
            if prefix not in groups:
                groups[prefix] = []
            groups[prefix].append(match)
        
        # Xử lý từng group
        for prefix, matches in groups.items():
            if len(matches) >= 2:  # Chỉ bảo vệ nếu có ít nhất 2 items
                # Sắp xếp theo vị trí
                matches.sort(key=lambda x: x.start())
                
                # Tìm điểm bắt đầu (từ đầu item đầu tiên)
                start_pos = matches[0].start()
                
                # Tìm điểm kết thúc (sau item cuối cùng)
                last_match = matches[-1]
                last_pos = last_match.end()
                
                # Tìm kết thúc của content của item cuối cùng
                remaining_text = content[last_pos:]
                
                # Tìm điểm kết thúc hợp lý
                end_patterns = [
                    r'\n\s*\n\s*(?!\d+\.\d+\.)',  # Đoạn mới không phải hierarchical numbering
                    r'\n\s*\n\s*(?:Điều|Chương|Mục|\d+\.)',  # Điều, Chương, Mục mới
                    r'$'  # Kết thúc văn bản
                ]
                
                end_pos = len(content)  # Mặc định
                for end_pattern in end_patterns:
                    end_match = re.search(end_pattern, remaining_text)
                    if end_match:
                        end_pos = last_pos + end_match.start()
                        break
                
                enumeration_ranges.append((start_pos, end_pos))
    
    return enumeration_ranges

def count_words(text):
    """
    Đếm số từ trong văn bản tiếng Việt
    """
    if not isinstance(text, str) or not text.strip():
        return 0
    
    # Sử dụng word_tokenize để đếm từ chính xác
    words = word_tokenize(text.strip(), format="text").split()
    return len(words)

def chunk_legal_article(content, min_length=64, max_length=256):
    # Tìm các khoảng chứa danh sách liệt kê cần bảo vệ
    enumeration_ranges = find_enumeration_ranges(content)
    
    # Pattern cải thiện để tránh nhầm lẫn với ngày tháng, số đánh phân cấp và số trong tiêu đề
    # Loại trừ: ngày tháng, số phân cấp (1.1.), số sau các từ khóa pháp lý
    pattern = r'(?<![\"\'""::\w])(?<!ngày\s)(?<!tháng\s)(?<!năm\s)(?<!Ngày\s)(?<!Tháng\s)(?<!Năm\s)(?<!\d\.)(?<!Điều\s)(?<!điều\s)(?<!Khoản\s)(?<!khoản\s)(?<!Chương\s)(?<!chương\s)(?<!Mục\s)(?<!mục\s)(?<!Tiêu\schí\s)(?<!tiêu\schí\s)(?<!Phần\s)(?<!phần\s)(?<!Bộ\s)(?<!bộ\s)(?<!Tờ\s)(?<!tờ\s)(?<!Số\s)(?<!số\s)(\b\d+\.\s)(?!\d)'
    
    # Tìm tất cả vị trí bắt đầu của các điểm số
    all_matches = list(re.finditer(pattern, content))
    
    # Loại bỏ các matches nằm trong khoảng liệt kê
    matches = []
    for match in all_matches:
        match_pos = match.start()
        is_in_enumeration = False
        
        for start, end in enumeration_ranges:
            if start <= match_pos <= end:
                is_in_enumeration = True
                break
        
        if not is_in_enumeration:
            matches.append(match)
    
    if not matches:
        # Nếu không có điểm số nào, kiểm tra độ dài và chia nếu cần
        if count_words(content.strip()) <= max_length:
            return [content.strip()]
        else:
            # Chia theo max_length nếu quá dài với logic cắt thông minh
            chunks = []
            text = content.strip()
            
            while count_words(text) > max_length:
                # Chia văn bản thành từ để xử lý theo từ
                words = word_tokenize(text, format="text").split()
                
                # Tìm vị trí cắt tốt nhất gần max_length từ
                cut_word_pos = max_length
                best_cut_word_pos = -1
                
                # Kiểm tra từ max_length về phía trước để tìm vị trí cắt tốt
                for i in range(min(cut_word_pos, len(words) - 1), max(min_length, 0), -1):
                    # Tạo lại văn bản từ 0 đến i để kiểm tra
                    partial_text = ' '.join(words[:i])
                    
                    # Cố gắng cắt ở dấu chấm câu (ưu tiên cao nhất)
                    sentence_endings = ['.', '!', '?']
                    for ending in sentence_endings:
                        if partial_text.endswith(ending):
                            # Kiểm tra xem phần sau có đủ từ không
                            remaining_words = len(words) - i
                            if remaining_words >= min_length:
                                best_cut_word_pos = i
                                break
                    if best_cut_word_pos != -1:
                        break
                
                # Nếu không tìm được dấu chấm câu, cắt ở vị trí max_length
                if best_cut_word_pos == -1:
                    # Kiểm tra xem phần sau có đủ từ không
                    remaining_words = len(words) - max_length
                    if remaining_words >= min_length:
                        best_cut_word_pos = max_length
                
                # Nếu vẫn không tìm được vị trí tốt hoặc phần sau quá ngắn, không cắt
                if best_cut_word_pos == -1 or best_cut_word_pos <= min_length:
                    # Không cắt, giữ nguyên text dài
                    chunks.append(text)
                    break
                
                # Cắt chunk và thêm vào danh sách
                chunk_words = words[:best_cut_word_pos]
                chunk = ' '.join(chunk_words).strip()
                chunks.append(chunk)
                
                # Cập nhật text cho vòng lặp tiếp theo
                remaining_words = words[best_cut_word_pos:]
                text = ' '.join(remaining_words).strip()
            
            # Thêm phần còn lại nếu có
            if text:
                chunks.append(text)
            
            return chunks
    
    chunks = []
    i = 0
    
    # Xử lý phần mở đầu không đánh số
    first_match_start = matches[0].start()
    opening_part = content[:first_match_start].strip()
    
    if opening_part:
        # Có phần mở đầu, kết hợp với chunk đầu tiên
        if i + 1 < len(matches):
            end_pos = matches[i + 1].start()
        else:
            end_pos = len(content)
        
        # Lấy chunk đầu tiên kèm phần mở đầu
        first_chunk_with_opening = content[:end_pos].strip()
        chunks.append(first_chunk_with_opening)
        i = 1
    
    while i < len(matches):
        start_pos = matches[i].start()
        current_chunk = ""
        
        # Tìm vị trí kết thúc ban đầu của chunk hiện tại
        if i + 1 < len(matches):
            end_pos = matches[i + 1].start()
        else:
            end_pos = len(content)
        
        # Lấy content của chunk hiện tại
        current_chunk = content[start_pos:end_pos].strip()
        
        # Kiểm tra và kết hợp các chunk tiếp theo để đạt min_length
        j = i + 1
        while count_words(current_chunk) < min_length and j < len(matches):
            # Mở rộng chunk để bao gồm chunk tiếp theo
            if j + 1 < len(matches):
                next_end_pos = matches[j + 1].start()
            else:
                next_end_pos = len(content)
            
            current_chunk = content[start_pos:next_end_pos].strip()
            j += 1
        
        # Nếu chunk vẫn nhỏ hơn min_length và đã hết matches, lấy hết content còn lại
        if count_words(current_chunk) < min_length and j >= len(matches):
            current_chunk = content[start_pos:].strip()
        
        # Kiểm tra final: nếu chunk vẫn quá ngắn, kết hợp với chunk trước đó
        if count_words(current_chunk) < min_length:
            if chunks:
                # Kết hợp với chunk trước đó, thậm chí có thể vượt max_length
                chunks[-1] = chunks[-1] + " " + current_chunk
                # Cập nhật chỉ số để bỏ qua các matches đã được xử lý
                i = j if j > i + 1 else i + 1
                continue
            else:
                # Nếu không có chunk trước đó, vẫn phải giữ chunk này
                pass
        
        # Kiểm tra nếu chunk quá dài, cắt ở max_length
        if count_words(current_chunk) > max_length:
            # Chia chunk thành từ để xử lý theo từ
            chunk_words = word_tokenize(current_chunk, format="text").split()
            
            # Tìm vị trí cắt tốt nhất gần max_length từ
            cut_word_pos = max_length
            best_cut_word_pos = -1
            
            # Kiểm tra từ max_length về phía trước để tìm vị trí cắt tốt
            for k in range(min(cut_word_pos, len(chunk_words) - 1), max(min_length, 0), -1):
                # Tạo lại văn bản từ 0 đến k để kiểm tra
                partial_text = ' '.join(chunk_words[:k])
                
                # Cố gắng cắt ở dấu chấm câu (ưu tiên cao nhất)
                sentence_endings = ['.', '!', '?']
                for ending in sentence_endings:
                    if partial_text.endswith(ending):
                        # Kiểm tra xem phần sau có đủ từ không
                        remaining_words = len(chunk_words) - k
                        if remaining_words >= min_length:
                            best_cut_word_pos = k
                            break
                if best_cut_word_pos != -1:
                    break
            
            # Nếu không tìm được dấu chấm câu, cắt ở vị trí max_length
            if best_cut_word_pos == -1:
                # Kiểm tra xem phần sau có đủ từ không
                remaining_words = len(chunk_words) - max_length
                if remaining_words >= min_length:
                    best_cut_word_pos = max_length
            
            # Nếu vẫn không tìm được vị trí tốt hoặc phần sau quá ngắn, không cắt
            if best_cut_word_pos == -1 or best_cut_word_pos <= min_length:
                # Không cắt, giữ nguyên chunk dài
                chunks.append(current_chunk)
            else:
                # Chia chunk thành 2 phần (không có overlap)
                first_chunk_words = chunk_words[:best_cut_word_pos]
                first_chunk = ' '.join(first_chunk_words).strip()
                
                remaining_words = chunk_words[best_cut_word_pos:]
                remaining_content = ' '.join(remaining_words).strip()
                
                chunks.append(first_chunk)
                
                # Xử lý phần còn lại
                if count_words(remaining_content) >= min_length:
                    # Kiểm tra nếu remaining_content vẫn quá dài, chia đệ quy
                    if count_words(remaining_content) > max_length:
                        # Chia đệ quy phần còn lại
                        remaining_chunks = chunk_legal_article(remaining_content, min_length, max_length)
                        chunks.extend(remaining_chunks)
                    else:
                        chunks.append(remaining_content)
                else:
                    # Nếu phần còn lại quá ngắn, kết hợp với chunk vừa thêm
                    if remaining_content.strip():
                        chunks[-1] = chunks[-1] + " " + remaining_content.strip()
        else:
            # Thêm chunk mà không có overlap
            chunks.append(current_chunk)
        
        # Cập nhật chỉ số để bỏ qua các matches đã được xử lý
        i = j if j > i + 1 else i + 1
    
    return chunks

def process_legal_corpus_with_chunks(legal_df, min_length=64, max_length=256):
    processed_data = []
    
    for idx, row in legal_df.iterrows():
        chunks = chunk_legal_article(row['content_Article'], min_length, max_length)
        
        for chunk_idx, chunk in enumerate(chunks):
            processed_data.append({
                'doc_id': row['doc_id'],
                'law_id': row['law_id'],
                'aid': row['aid'],
                'chunk_id': chunk_idx,
                'original_content': row['content_Article'],
                'chunk_content': chunk,
                'chunk_length': count_words(chunk)
            })
    
    return pd.DataFrame(processed_data)

train_df, legal_df = load_data('train.json', 'legal_corpus.json')

# Examine the data
print(f"Train data shape: {train_df.shape}")
print(f"Legal corpus shape: {legal_df.shape}")

# Display sample data
print("\nSample question:")
print(train_df['question'].iloc[0])

print("\nSample legal article:")
print(legal_df['content_Article'].iloc[0])

legal_df_with_chunks = process_legal_corpus_with_chunks(legal_df, min_length=64, max_length=256)
print(f"Legal corpus with chunks shape: {legal_df_with_chunks.shape}")

def normalize_vietnamese_text(text):
    if not isinstance(text, str):
        return ""
    # Sử dụng text_normalize từ underthesea
    text = text_normalize(text)
    
    # Loại bỏ khoảng trắng thừa
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()
    
    return text

def word_segment_vietnamese_text(text):
    """
    Thực hiện word segmentation cho văn bản tiếng Việt
    """
    if not isinstance(text, str) or not text.strip():
        return ""
    
    return word_tokenize(text, format="text")

def process_text_pipeline(text):
    """
    Pipeline xử lý văn bản: chuẩn hóa → word segmentation
    """
    # Bước 1: Chuẩn hóa văn bản
    normalized_text = normalize_vietnamese_text(text)
    
    # Bước 2: Word segmentation
    segmented_text = word_segment_vietnamese_text(normalized_text)
    
    return {
        "original_text": text,
        "normalized_text": normalized_text,
        "segmented_text": segmented_text
    }

def process_questions_with_pipeline(train_df, batch_size=16):
    """
    Xử lý tất cả câu hỏi trong train dataset theo batch
    """
    processed_questions = []
    
    print("Processing questions...")
    
    # Lấy tất cả texts để xử lý batch
    questions = train_df['question'].tolist()
    qids = train_df['qid'].tolist()
    relevant_laws = train_df['relevant_laws'].tolist()
    
    # Xử lý theo batch
    for i in tqdm(range(0, len(questions), batch_size), desc="Processing question batches"):
        batch_questions = questions[i:i + batch_size]
        batch_qids = qids[i:i + batch_size]
        batch_relevant_laws = relevant_laws[i:i + batch_size]
        
        # Bước 1: Chuẩn hóa văn bản theo batch
        normalized_texts = [normalize_vietnamese_text(text) for text in batch_questions]
        
        # Bước 2: Word segmentation theo batch
        segmented_texts = [word_segment_vietnamese_text(text) for text in normalized_texts]
        
        # Kết hợp kết quả
        for j in range(len(batch_questions)):
            processed_questions.append({
                'qid': batch_qids[j],
                'relevant_laws': batch_relevant_laws[j],
                'original_text': batch_questions[j],
                'normalized_text': normalized_texts[j],
                'segmented_text': segmented_texts[j],
            })
    
    return pd.DataFrame(processed_questions)

def process_legal_content_with_pipeline(legal_df_with_chunks, batch_size=16):
    """
    Xử lý legal content theo batch
    """
    processed_content = []
    
    print("Processing legal content...")
    
    # Lấy tất cả dữ liệu cần thiết
    chunk_contents = legal_df_with_chunks['chunk_content'].tolist()
    doc_ids = legal_df_with_chunks['doc_id'].tolist()
    law_ids = legal_df_with_chunks['law_id'].tolist()
    aids = legal_df_with_chunks['aid'].tolist()
    chunk_ids = legal_df_with_chunks['chunk_id'].tolist()
    chunk_lengths = legal_df_with_chunks['chunk_length'].tolist()
    
    # Xử lý theo batch
    for i in tqdm(range(0, len(chunk_contents), batch_size), desc="Processing legal content batches"):
        batch_contents = chunk_contents[i:i + batch_size]
        batch_doc_ids = doc_ids[i:i + batch_size]
        batch_law_ids = law_ids[i:i + batch_size]
        batch_aids = aids[i:i + batch_size]
        batch_chunk_ids = chunk_ids[i:i + batch_size]
        batch_chunk_lengths = chunk_lengths[i:i + batch_size]
        
        # Bước 1: Chuẩn hóa văn bản theo batch
        normalized_texts = [normalize_vietnamese_text(text) for text in batch_contents]
        
        # Bước 2: Word segmentation theo batch
        segmented_texts = [word_segment_vietnamese_text(text) for text in normalized_texts]
        
        # Kết hợp kết quả
        for j in range(len(batch_contents)):
            processed_content.append({
                'doc_id': batch_doc_ids[j],
                'law_id': batch_law_ids[j],
                'aid': batch_aids[j],
                'chunk_id': batch_chunk_ids[j],
                'chunk_length': batch_chunk_lengths[j],
                'original_text': batch_contents[j],
                'normalized_text': normalized_texts[j],
                'segmented_text': segmented_texts[j]
            })
    
    return pd.DataFrame(processed_content)

# Sử dụng batch processing với batch size tối ưu
batch_size = 16  # Giảm batch size để xử lý word segmentation hiệu quả hơn

processed_questions_df = process_questions_with_pipeline(train_df, batch_size=batch_size)
processed_legal_df = process_legal_content_with_pipeline(legal_df_with_chunks, batch_size=batch_size)
print(f"Processed questions shape: {processed_questions_df.shape}")
print(f"Processed legal content shape: {processed_legal_df.shape}")

class BGEM3RAGSystem:
    """
    Hệ thống RAG kết hợp BM25 và BGE-M3 để tìm kiếm văn bản pháp luật
    Giai đoạn 1: BM25 filter nhanh
    Giai đoạn 2: BGE-M3 rerank chính xác
    """
    
    def __init__(self, model_name: str = "BAAI/bge-m3", cache_dir: str = "./cache"):
        """
        Khởi tạo RAG system
        
        Args:
            model_name: Tên model BGE-M3
            cache_dir: Thư mục lưu cache
        """
        self.model_name = model_name
        self.cache_dir = cache_dir
        self.model = None
        self.legal_embeddings = None
        self.legal_metadata = None
        self.faiss_index = None
        
        # BM25 components
        self.bm25_index = None
        self.bm25_corpus = None
        
        # Tạo thư mục cache nếu chưa có
        os.makedirs(cache_dir, exist_ok=True)
        
        # Đường dẫn các file cache
        self.embeddings_path = os.path.join(cache_dir, "legal_embeddings.pkl")
        self.metadata_path = os.path.join(cache_dir, "legal_metadata.pkl")
        self.faiss_index_path = os.path.join(cache_dir, "faiss_index.bin")
        self.bm25_index_path = os.path.join(cache_dir, "bm25_index.pkl")
        self.bm25_corpus_path = os.path.join(cache_dir, "bm25_corpus.pkl")
        
    def initialize_model(self):
        """Khởi tạo BGE-M3 model"""
        print(f"Initializing BGE-M3 model: {self.model_name}")
        
        try:
            self.model = BGEM3FlagModel(
                self.model_name,
                use_fp16=True,  # Sử dụng fp16 để tiết kiệm memory
                device='cuda' if torch.cuda.is_available() else 'cpu'
            )
            print("BGE-M3 model initialized successfully")
        except Exception as e:
            print(f"Failed to initialize BGE-M3 model: {e}")
            raise
    
    def encode_texts(self, texts: List[str], batch_size: int = 8) -> np.ndarray:
        """
        Encode danh sách văn bản thành embeddings
        
        Args:
            texts: Danh sách văn bản cần encode
            batch_size: Kích thước batch để xử lý
            
        Returns:
            numpy array chứa embeddings
        """
        if not self.model:
            self.initialize_model()
            
        embeddings = []
        
        # print(f"Encoding {len(texts)} texts with batch size {batch_size}")
        
        for i in tqdm(range(0, len(texts), batch_size), desc="Encoding texts", disable=True):
            batch_texts = texts[i:i + batch_size]
            
            try:
                with suppress_stdout_stderr():
                    # BGE-M3 encode trả về dict với 'dense_vecs'
                    batch_embeddings = self.model.encode(
                        batch_texts,
                        batch_size=batch_size,
                        max_length=512,  # Độ dài tối đa cho legal text
                        return_dense=True,
                        return_sparse=False,
                        return_colbert_vecs=False
                    )
                
                # Lấy dense embeddings
                if isinstance(batch_embeddings, dict):
                    dense_embeddings = batch_embeddings['dense_vecs']
                else:
                    dense_embeddings = batch_embeddings
                    
                embeddings.extend(dense_embeddings)
                
            except Exception as e:
                print(f"Error encoding batch {i//batch_size}: {e}")
                # Thêm zero embeddings cho batch bị lỗi
                batch_size_actual = len(batch_texts)
                zero_embeddings = np.zeros((batch_size_actual, 1024))  # BGE-M3 có 1024 dimensions
                embeddings.extend(zero_embeddings)
        
        return np.array(embeddings)
    
    def _build_bm25_index(self, processed_legal_df: pd.DataFrame):
        """Xây dựng BM25 index từ segmented text"""
        print("Building BM25 index...")
        
        # Chuẩn bị corpus cho BM25
        self.bm25_corpus = []
        for idx, row in processed_legal_df.iterrows():
            # Tokenize segmented text cho BM25
            tokens = row['segmented_text'].split()
            self.bm25_corpus.append(tokens)
        
        # Tạo BM25 index
        self.bm25_index = BM25Okapi(self.bm25_corpus)
        print(f"BM25 index built with {len(self.bm25_corpus)} documents")
    
    def build_legal_knowledge_base(self, processed_legal_df: pd.DataFrame, force_rebuild: bool = False):
        """
        Xây dựng knowledge base từ dữ liệu legal đã xử lý
        Bao gồm cả BM25 và BGE-M3 indexes
        
        Args:
            processed_legal_df: DataFrame chứa dữ liệu legal đã được xử lý
            force_rebuild: Có rebuild lại hay không nếu cache đã tồn tại
        """
        
        # Kiểm tra cache tồn tại
        if not force_rebuild and self._cache_exists():
            print("Loading legal knowledge base from cache...")
            self._load_from_cache()
            return
        
        print("Building legal knowledge base...")
        
        # Chuẩn bị dữ liệu
        texts_to_embed = []
        metadata = []
        
        for idx, row in processed_legal_df.iterrows():
            # Sử dụng segmented_text để embedding
            text_for_embedding = row['segmented_text']
            texts_to_embed.append(text_for_embedding)
            
            # Metadata để tracking
            metadata.append({
                'doc_id': row['doc_id'],
                'law_id': row['law_id'],
                'aid': row['aid'],
                'chunk_id': row['chunk_id'],
                'chunk_length': row['chunk_length'],
                'original_text': row['original_text'],
                'normalized_text': row['normalized_text'],
                'segmented_text': row['segmented_text'],
                'index': idx
            })
        
        # Xây dựng BM25 index
        self._build_bm25_index(processed_legal_df)
        
        # Tạo embeddings
        print(f"Creating embeddings for {len(texts_to_embed)} legal chunks...")
        self.legal_embeddings = self.encode_texts(texts_to_embed, batch_size=8)
        self.legal_metadata = metadata
        
        # Xây dựng FAISS index
        self._build_faiss_index()
        
        # Lưu cache
        self._save_to_cache()
        
        print("Legal knowledge base built successfully!")
    
    def _build_faiss_index(self):
        """Xây dựng FAISS index để tìm kiếm nhanh"""
        print("Building FAISS index...")
        
        # Chuẩn hóa embeddings
        embeddings_normalized = self.legal_embeddings.astype('float32')
        faiss.normalize_L2(embeddings_normalized)
        
        # Tạo FAISS index (Inner Product cho cosine similarity)
        dimension = embeddings_normalized.shape[1]
        self.faiss_index = faiss.IndexFlatIP(dimension)
        self.faiss_index.add(embeddings_normalized)
        
        print(f"FAISS index built with {self.faiss_index.ntotal} vectors")
    
    def _cache_exists(self) -> bool:
        """Kiểm tra xem cache có tồn tại không"""
        return (os.path.exists(self.embeddings_path) and 
                os.path.exists(self.metadata_path) and 
                os.path.exists(self.faiss_index_path) and
                os.path.exists(self.bm25_index_path) and
                os.path.exists(self.bm25_corpus_path))
    
    def _save_to_cache(self):
        """Lưu embeddings, metadata và BM25 index vào cache"""
        print("Saving to cache...")
        
        # Lưu embeddings
        with open(self.embeddings_path, 'wb') as f:
            pickle.dump(self.legal_embeddings, f)
        
        # Lưu metadata
        with open(self.metadata_path, 'wb') as f:
            pickle.dump(self.legal_metadata, f)
        
        # Lưu BM25 index
        with open(self.bm25_index_path, 'wb') as f:
            pickle.dump(self.bm25_index, f)
        
        # Lưu BM25 corpus
        with open(self.bm25_corpus_path, 'wb') as f:
            pickle.dump(self.bm25_corpus, f)
        
        # Lưu FAISS index
        faiss.write_index(self.faiss_index, self.faiss_index_path)
        
        print("Cache saved successfully")
    
    def _load_from_cache(self):
        """Load embeddings, metadata và BM25 index từ cache"""
        print("Loading from cache...")
        
        # Load embeddings
        with open(self.embeddings_path, 'rb') as f:
            self.legal_embeddings = pickle.load(f)
        
        # Load metadata
        with open(self.metadata_path, 'rb') as f:
            self.legal_metadata = pickle.load(f)
        
        # Load BM25 index
        with open(self.bm25_index_path, 'rb') as f:
            self.bm25_index = pickle.load(f)
        
        # Load BM25 corpus
        with open(self.bm25_corpus_path, 'rb') as f:
            self.bm25_corpus = pickle.load(f)
        
        # Load FAISS index
        self.faiss_index = faiss.read_index(self.faiss_index_path)
        
        print("Cache loaded successfully")
    
    def _bm25_filter(self, query: str, top_k_filter: int = 100) -> List[int]:
        """
        BM25 filtering để lọc ra tập candidates
        
        Args:
            query: Câu query đã segmented
            top_k_filter: Số lượng documents để filter
            
        Returns:
            List of document indices được filter bởi BM25
        """
        query_tokens = query.split()
        bm25_scores = self.bm25_index.get_scores(query_tokens)
        
        # Lấy top_k_filter indices có score cao nhất
        top_indices = np.argsort(bm25_scores)[::-1][:top_k_filter]
        return top_indices.tolist()
    
    def batch_search_questions(self, questions_df: pd.DataFrame, bm25_filter_size: int = 200) -> pd.DataFrame:
        """
        Tìm kiếm batch cho nhiều câu hỏi sử dụng hybrid BM25 + BGE-M3
        
        Logic mới:
        1. BM25 filtering để lọc ra top candidates  
        2. BGE-M3 để tính similarity score cho các candidates
        3. Deduplicate theo aid (chỉ giữ chunk có score cao nhất cho mỗi aid)
        4. Lấy kết quả có score cao nhất
        5. Lấy tất cả kết quả có score >= 90% của score cao nhất
        6. Giới hạn tối đa 3 kết quả cuối cùng
        
        Args:
            questions_df: DataFrame chứa câu hỏi đã được xử lý
            bm25_filter_size: Số lượng documents để BM25 filter trước BGE-M3 (default: 200)
            
        Returns:
            DataFrame chứa kết quả tìm kiếm với các cột:
            - qid, question_text, question_segmented
            - doc_id, law_id, aid, chunk_id  
            - similarity_score, rank
            - original_text, normalized_text, segmented_text
        """
        print(f"Processing {len(questions_df)} questions for hybrid retrieval...")
        print(f"BM25 filter size: {bm25_filter_size}")
    
        queries = questions_df['segmented_text'].tolist()
        qids = questions_df['qid'].tolist()
        original_texts = questions_df['original_text'].tolist()
        
        results = []
        
        # Process queries in batches để tối ưu memory
        batch_size = 32
        for batch_start in tqdm(range(0, len(queries), batch_size), desc="Processing query batches"):
            batch_end = min(batch_start + batch_size, len(queries))
            batch_queries = queries[batch_start:batch_end]
            batch_qids = qids[batch_start:batch_end]
            batch_original_texts = original_texts[batch_start:batch_end]
            
            # Giai đoạn 1: BM25 filtering cho mỗi query trong batch
            batch_filtered_indices = []
            for query in batch_queries:
                filtered_indices = self._bm25_filter(query, bm25_filter_size)
                batch_filtered_indices.append(filtered_indices)
            
            # Giai đoạn 2: BGE-M3 reranking
            # Encode batch queries
            batch_query_embeddings = self.encode_texts(batch_queries, batch_size=len(batch_queries)).astype('float32')
            faiss.normalize_L2(batch_query_embeddings)
            
            # Rerank for each query trong batch
            for i, (qid, original_text, query, query_embedding, filtered_indices) in enumerate(zip(
                batch_qids, batch_original_texts, batch_queries, batch_query_embeddings, batch_filtered_indices
            )):
                if len(filtered_indices) == 0:
                    continue
                    
                # Lấy embeddings của filtered documents
                filtered_embeddings = self.legal_embeddings[filtered_indices].astype('float32')
                faiss.normalize_L2(filtered_embeddings)
                
                # Compute cosine similarity
                similarities = np.dot(filtered_embeddings, query_embedding)
                
                # Sort by similarity
                sorted_indices = np.argsort(similarities)[::-1]
                
                # Deduplicate by aid - chỉ giữ chunk có score cao nhất cho mỗi aid
                seen_aids = set()
                candidate_results = []
                
                for idx_in_filtered in sorted_indices:
                    original_idx = filtered_indices[idx_in_filtered]
                    similarity_score = float(similarities[idx_in_filtered])
                    
                    metadata = self.legal_metadata[original_idx]
                    aid = metadata['aid']
                    
                    # Chỉ thêm nếu aid chưa thấy
                    if aid not in seen_aids:
                        seen_aids.add(aid)
                        result_metadata = metadata.copy()
                        result_metadata.update({
                            'qid': qid,
                            'question_text': original_text,
                            'question_segmented': query,
                            'similarity_score': similarity_score,
                        })
                        candidate_results.append(result_metadata)
                
                if candidate_results:
                    # Sắp xếp theo score giảm dần
                    candidate_results.sort(key=lambda x: x['similarity_score'], reverse=True)
                    
                    # Lấy score cao nhất
                    top_score = candidate_results[0]['similarity_score']
                    threshold_90_percent = top_score * 0.9
                    
                    # Lấy tất cả kết quả có score >= 90% của score cao nhất
                    selected_results = []
                    for result in candidate_results:
                        if result['similarity_score'] >= threshold_90_percent:
                            selected_results.append(result)
                        else:
                            break  # Do đã sắp xếp giảm dần, không cần kiểm tra tiếp
                    
                    # Giới hạn tối đa 3 kết quả
                    selected_results = selected_results[:3]
                    
                    # Gán rank
                    for rank, result in enumerate(selected_results, 1):
                        result['rank'] = rank
                    
                    # Thêm vào kết quả chung
                    results.extend(selected_results)
        
        return pd.DataFrame(results)
    
    def save_results(self, search_results_df: pd.DataFrame, output_path: str = "./rag_results.json"):
        """
        Lưu kết quả tìm kiếm (format chi tiết)
        
        Args:
            search_results_df: Kết quả tìm kiếm
            output_path: Đường dẫn file output
        """
        # Chuyển đổi DataFrame thành format JSON
        results_dict = {}
        
        for qid in search_results_df['qid'].unique():
            qid_results = search_results_df[search_results_df['qid'] == qid]
        
            results_dict[str(qid)] = { 
                'question': qid_results.iloc[0]['question_text'],
                'retrieved_legal_texts': []
            }
        
            for _, row in qid_results.iterrows():
                results_dict[str(qid)]['retrieved_legal_texts'].append({
                    'doc_id': row['doc_id'],
                    'law_id': row['law_id'],
                    'aid': row['aid'],
                    'chunk_id': row['chunk_id'],
                    'similarity_score': float(row['similarity_score']),  # ép về float chuẩn
                    'rank': int(row['rank']),
                    'content': row['original_text']
                })

        
        # Lưu file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results_dict, f, ensure_ascii=False, indent=2)
        
        print(f"Results saved to {output_path}")
    
    def save_results_simple_format(self, search_results_df: pd.DataFrame, output_path: str = "./rag_results_simple.json"):
        """
        Lưu kết quả tìm kiếm theo format đơn giản
        Format: [{"qid": 1, "relevant_laws": [100, 101, 102]}, ...]
        
        Args:
            search_results_df: Kết quả tìm kiếm
            output_path: Đường dẫn file output
        """
        results_list = []
        
        for qid in sorted(search_results_df['qid'].unique()):
            qid_results = search_results_df[search_results_df['qid'] == qid]
            
            # Lấy danh sách aid được retrieve, sắp xếp theo rank
            retrieved_aids = qid_results.sort_values('rank')['aid'].tolist()
            
            results_list.append({
                "qid": int(qid),
                "relevant_laws": retrieved_aids
            })
        
        # Lưu file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results_list, f, ensure_ascii=False, indent=2)
        
        print(f"Simple format results saved to {output_path}")


print("\n" + "="*60)
print("STARTING RAG SYSTEM WITH BGE-M3")
print("="*60)

# Khởi tạo RAG system
rag_system = BGEM3RAGSystem(cache_dir="/kaggle/input/weight-drill")

# Xây dựng knowledge base
rag_system.build_legal_knowledge_base(processed_legal_df, force_rebuild=False)

# Tìm kiếm cho tất cả câu hỏi
search_results_df = rag_system.batch_search_questions(
    processed_questions_df,
    bm25_filter_size=200
)

# Lưu kết quả
rag_system.save_results(search_results_df)  # Format chi tiết
rag_system.save_results_simple_format(search_results_df)  # Format đơn giản

print(f"\nRAG System initialized successfully!")
print(f"Knowledge base contains {len(processed_legal_df)} legal chunks")
print(f"Search results shape: {search_results_df.shape}")
print(f"New retrieval logic: Taking top result + results with score >= 90% of top score (max 3 per query)")

# Evaluation
def evaluate_retrieval(train_df, search_results_df):
    """Đánh giá kết quả retrieval theo macro F2 score"""
    results = []
    
    for qid in train_df['qid'].unique():
        # Lấy relevant laws từ train_df
        relevant_laws = set(train_df[train_df['qid'] == qid]['relevant_laws'].iloc[0])
        
        # Lấy retrieved aids từ search_results_df
        retrieved_data = search_results_df[search_results_df['qid'] == qid]
        retrieved_aids = set(retrieved_data['aid'].tolist())
        
        # Tính metrics
        true_positives = len(relevant_laws.intersection(retrieved_aids))
        precision = true_positives / len(retrieved_aids) if retrieved_aids else 0
        recall = true_positives / len(relevant_laws) if relevant_laws else 0
        
        # Tính F2 score: F2 = (5 × Precision × Recall) / (4 × Precision + Recall)
        # F2 gives more weight to recall than precision (beta=2), emphasizing the importance 
        # of not missing relevant legal articles in retrieval tasks
        f2 = (5 * precision * recall) / (4 * precision + recall) if (4 * precision + recall) > 0 else 0
        
        results.append({
            'qid': qid,
            'relevant_count': len(relevant_laws),
            'retrieved_count': len(retrieved_aids),
            'true_positives': true_positives,
            'precision': precision,
            'recall': recall,
            'f2': f2
        })
    
    return pd.DataFrame(results)

def print_evaluation_details(eval_df):
    """In chi tiết kết quả evaluation"""
    print(f"\nDetailed Evaluation Results:")
    print(f"{'='*50}")
    print(f"Number of queries evaluated: {len(eval_df)}")
    print(f"")
    
    # Macro-averaged metrics (as required by the evaluation methodology)
    print(f"Macro-averaged Metrics:")
    print(f"  Precision: {eval_df['precision'].mean():.4f} (±{eval_df['precision'].std():.4f})")
    print(f"  Recall:    {eval_df['recall'].mean():.4f} (±{eval_df['recall'].std():.4f})")
    print(f"  F2 Score:  {eval_df['f2'].mean():.4f} (±{eval_df['f2'].std():.4f})")
    print(f"")

eval_df = evaluate_retrieval(train_df, search_results_df)
print_evaluation_details(eval_df)