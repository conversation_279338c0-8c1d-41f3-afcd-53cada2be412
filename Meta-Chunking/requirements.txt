absl-py==2.1.0
accelerate==0.32.1
aiohappyeyeballs==2.3.4
aiohttp==3.10.0
aiosignal==1.3.1
annotated-types==0.7.0
anyio==4.4.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
asttokens==2.4.1
async-timeout==4.0.3
attrs==23.2.0
beautifulsoup4==4.12.3
certifi==2024.7.4
cffi==1.16.0
charset-normalizer==3.3.2
click==8.1.7
cloudpickle==3.0.0
cmake==3.30.2
comm==0.2.2
contourpy==1.2.1
cramjam==2.8.3
cycler==0.12.1
dataclasses-json==0.6.7
datasets==2.20.0
debugpy==1.8.5
decorator==5.1.1
Deprecated==1.2.14
dill==0.3.8
diskcache==5.6.3
distro==1.9.0
einops==0.8.0
elastic-transport==8.13.1
elasticsearch==8.14.0
environs==9.5.0
evaluate==0.4.2
exceptiongroup==1.2.2
executing==2.0.1
fastapi==0.112.0
fastparquet==2024.5.0
filelock==3.15.4
FlagEmbedding==1.2.10
fonttools==4.53.1
frozenlist==1.4.1
fsspec==2024.5.0
gradio==4.44.1
greenlet==3.0.3
grpcio==1.58.0
h11==0.14.0
httpcore==1.0.5
httptools==0.6.1
httpx==0.27.0
huggingface-hub==0.23.4
idna==3.7
infomap==2.8.0
interegular==0.3.3
ipykernel==6.29.5
ipython==8.26.0
jedi==0.19.1
jieba==0.42.1
Jinja2==3.1.4
joblib==1.4.2
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2023.12.1
jupyter_client==8.6.2
jupyter_core==5.7.2
kiwisolver==1.4.5
langchain==0.1.4
langchain-community==0.0.20
langchain-core==0.1.23
langsmith==0.0.87
lark==1.1.9
llama-index==0.9.32
llvmlite==0.43.0
lm-format-enforcer==0.10.3
loguru==0.7.2
MarkupSafe==2.1.5
marshmallow==3.21.3
matplotlib==3.9.1.post1
matplotlib-inline==0.1.7
milvus==2.3.3
minio==7.2.7
mpmath==1.3.0
msgpack==1.0.8
multidict==6.0.5
multiprocess==0.70.16
mypy-extensions==1.0.0
nest-asyncio==1.6.0
networkx==3.3
ninja==********
nltk==3.8.1
numba==0.60.0
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==*********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==**********
nvidia-cusparse-cu12==**********
nvidia-ml-py==12.555.43
nvidia-nccl-cu12==2.20.5
nvidia-nvjitlink-cu12==12.5.82
nvidia-nvtx-cu12==12.1.105
openai==1.38.0
outlines==0.0.46
packaging==23.2
pandas==2.2.2
parso==0.8.4
pexpect==4.9.0
pillow==10.4.0
platformdirs==4.2.2
prometheus-fastapi-instrumentator==7.0.0
prometheus_client==0.20.0
prompt_toolkit==3.0.47
protobuf==5.27.3
psutil==6.0.0
ptyprocess==0.7.0
pure_eval==0.2.3
py-cpuinfo==9.0.0
pyairports==2.1.1
pyarrow==17.0.0
pyarrow-hotfix==0.6
pycountry==24.6.1
pycparser==2.22
pycryptodome==3.20.0
pydantic==2.8.2
pydantic_core==2.20.1
Pygments==2.18.0
pymilvus==2.3.3
pyparsing==3.1.2
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
pytz==2024.1
PyYAML==6.0.1
pyzmq==26.0.3
ray==2.34.0
referencing==0.35.1
regex==2024.5.15
requests==2.32.3
rouge_score==0.1.2
rpds-py==0.20.0
safetensors==0.4.3
scikit-learn==1.5.1
scipy==1.14.0
sentence-transformers==3.0.1
sentencepiece==0.2.0
six==1.16.0
sniffio==1.3.1
soupsieve==2.5
SQLAlchemy==2.0.31
stack-data==0.6.3
starlette==0.37.2
sympy==1.12.1
tenacity==8.5.0
text2vec==1.2.9
threadpoolctl==3.5.0
tiktoken==0.7.0
tokenizers==0.19.1
torch==2.4.0
torchvision==0.19.0
tornado==6.4.1
tqdm==4.66.4
traitlets==5.14.3
transformers==4.44.0
triton==3.0.0
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2024.1
ujson==5.10.0
urllib3==2.2.2
uvicorn==0.30.5
uvloop==0.19.0
vllm==0.5.4
vllm-flash-attn==2.6.1
watchfiles==0.23.0
wcwidth==0.2.13
websockets==12.0
wrapt==1.16.0
xformers==0.0.27.post2
xxhash==3.4.1
yarl==1.9.4
