你是一位新闻编辑，现在，你被提供了1篇新闻的摘要，和几个问题，请分别根据新闻的摘要回答这些问题。用list的格式输出答案。以下是个例子：

新闻摘要:

2023年7月27日，42位来自9个国家的华裔青年在云南腾冲的普洱茶研学基地参观学习，体验了采茶、压茶、包茶饼等传统制茶工艺，感受到浓厚的普洱茶历史文化。活动由云南省人民政府侨务办公室主办。腾冲市以其悠久的种茶历史和丰富的茶文化，吸引了众多海外华裔青年的参与。

问题：

["参观学习的华裔青年共有多少人？","这些华裔青年来自几个国家？","华裔青年们的领队是谁？","这次活动是由谁主办的？","腾冲市吸引海外华裔青年的特点是什么？"],

回答:

参观学习的华裔青年共有多少人？
<response>
42人
</response>

这些华裔青年来自几个国家？
<response>
9个  
</response>

华裔青年们的领队是谁？
<response>
无法推断
</response>

这次活动是由谁主办的？
<response>
云南省人民政府侨务办公室
</response>

腾冲市吸引海外华裔青年的特点是什么？
<response>
悠久的种茶历史和丰富的茶文化
</response>


现在新闻的摘要是：

{context}

问题：

{questions}

请给出根据新闻的摘要的回答（回答的文本写在<response></response>之间。请注意，用一两个词或者非常简短的语句回答问题，不要添加多余的词。遇见无法回答的问题，请说：“无法推断”）

