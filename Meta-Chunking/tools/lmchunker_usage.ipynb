{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Examples of using lmchunker package"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/users/u2023000898/envs/mypypi/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "Sliding Window Attention is enabled but not implemented for `sdpa`; unexpected results may be encountered.\n"]}, {"data": {"text/plain": ["Qwen2ForCausalLM(\n", "  (model): Qwen2Model(\n", "    (embed_tokens): Embedding(151936, 1536)\n", "    (layers): ModuleList(\n", "      (0-27): 28 x Qwen2Decoder<PERSON>ayer(\n", "        (self_attn): Qwen2Attention(\n", "          (q_proj): Linear(in_features=1536, out_features=1536, bias=True)\n", "          (k_proj): Linear(in_features=1536, out_features=256, bias=True)\n", "          (v_proj): Linear(in_features=1536, out_features=256, bias=True)\n", "          (o_proj): Linear(in_features=1536, out_features=1536, bias=False)\n", "        )\n", "        (mlp): Qwen2MLP(\n", "          (gate_proj): Linear(in_features=1536, out_features=8960, bias=False)\n", "          (up_proj): Linear(in_features=1536, out_features=8960, bias=False)\n", "          (down_proj): Linear(in_features=8960, out_features=1536, bias=False)\n", "          (act_fn): SiLU()\n", "        )\n", "        (input_layernorm): Qwen2RMSNorm((1536,), eps=1e-06)\n", "        (post_attention_layernorm): Qwen2RMSNorm((1536,), eps=1e-06)\n", "      )\n", "    )\n", "    (norm): Qwen2RMSNorm((1536,), eps=1e-06)\n", "    (rotary_emb): Qwen2RotaryEmbedding()\n", "  )\n", "  (lm_head): Linear(in_features=1536, out_features=151936, bias=False)\n", ")"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch\n", "from transformers import AutoModelForCausalLM, AutoTokenizer\n", "model_name_or_path='/users/u2023000898/model/Qwen2.5-1.5B-Instruct' \n", "device_id = 1   \n", "device = torch.device(f'cuda:{device_id}' if torch.cuda.is_available() and torch.cuda.device_count() > device_id else 'cpu')  \n", "small_tokenizer = AutoTokenizer.from_pretrained(model_name_or_path,trust_remote_code=True)  \n", "small_model = AutoModelForCausalLM.from_pretrained(model_name_or_path, trust_remote_code=True).to(device)  \n", "small_model.eval()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> Segments the given text into chunks based on the specified method and parameters.\n", "\n", "Parameters:\n", "\n", "necessary\n", "- text: Text that needs to be segmented\n", "- small_model: The small language model used for segmentation\n", "- small_tokenizer: The tokenizer used for text tokenization\n", "- language: en or zh\n", "\n", "optional\n", "- methodth: The LLM chunking method that needs to be used, ['ppl','ms','lumber_ms']\n", "- threshold: The threshold for controlling PPL Chunking is inversely proportional to the chunk length; the smaller the threshold, the shorter the chunk length.\n", "- dynamic_merge: no or yes\n", "- target_size: If dynamic_merge='yes', then the chunk length value needs to be set\n", "- batch_size: The length of a single document processed at a time, used to optimize GPU memory usage when processing longer documents\n", "- max_txt_size: The total context length that can be considered or the maximum length that the GPU memory can accommodate\n", "\n", "Returns:\n", "- List[str]: A list of segmented text chunks\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Building prefix dict from the default dictionary ...\n", "Loading model from cache /tmp/jieba.cache\n", "Loading model cost 0.648 seconds.\n", "Prefix dict has been built successfully.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["111 [[0, 1, 2], [3, 4], [5, 6], [7, 8, 9, 10], [11, 12, 13], [14, 15], [16]]\n", "The program execution time is: 9.39937448501587 seconds.\n", "Number 1:  2023-08-01 10:47，正文：通气会现场 来源：湖南高院7月31日，湖南高院联合省司法厅召开新闻通气会。湖南高院副院长杨翔，省委依法治省办成员、省司法厅党组成员、副厅长杨龙金通报2022年湖南省行政机关负责人出庭应诉有关情况，并发布5个典型案例。2022年，全省经人民法院通知出庭的行政机关负责人出庭应诉率提升至96.5%。\n", "Number 2:  杨翔介绍，从出庭应诉数量看，负责人出庭应诉意识普遍提升。2022年，全省法院共发出行政机关负责人出庭应诉通知书4228份，行政机关负责人到庭应诉4018件。\n", "Number 3:  行政机关负责人参加调查询问1117件，参与案件协调化解741件。与2021年相比，行政机关负责人到庭应诉和参加调查询问等案件增加2802件。\n", "Number 4:  从地区分布情况来看，全省各地经人民法院通知的行政机关负责人出庭应诉率均达到90%以上，较往年有明显提升。2022年，从行政管理领域看，全省法院制发负责人出庭应诉通知书的案件所涉行政管理领域较为集中，自然资源、社会保障、公安、市场监管等部门负责人出庭应诉的案件数量较多。从涉案行政行为看，被诉行为类型相对集中。排名前五的行政行为类型依次为行政征收或征用类案件、行政确认类案件、不履行法定职责类案件、行政处罚类案件及行政登记类案件。\n", "Number 5:  从出庭应诉负责人层级比例看，基层行政机关负责人出庭应诉占比较高。县市区及乡镇负责人出庭应诉数量占全部出庭应诉案件数的80.8%。杨龙金介绍，为进一步加强和完善负责人出庭应诉制度建设，省委依法治省办、省法院、省司法厅联合印发《关于进一步推进行政机关负责人出庭应诉的工作方案》（以下简称《工作方案》），推动省政府出台《湖南省行政应诉工作规定》并召开全省行政应诉工作会议，依托府院联动，推动行政机关负责人出庭应诉工作有序开展。\n", "Number 6:  湖南高院、省司法厅根据最高人民法院相关司法解释，在《工作方案》中统一了行政机关负责人出庭应诉的认定标准和计算方式，实现了全省负责人出庭应诉工作的标准化和规范化。同时，推动将行政机关负责人出庭应诉情况纳入省绩效考核、平安建设、市域社会治理等考核指标体系，进一步压实出庭应诉主体责任。\n", "Number 7:  《工作方案》还明确将行政机关负责人参与调解和解并实质化解争议的案件视为已履行出庭应诉义务，既提高了负责人出庭应诉的积极性，也有力维护了当事人合法权益，促进经济社会和谐稳定。\n"]}], "source": ["from lmchunker import chunker\n", "import json\n", "with open('data/example1.json', 'r', encoding='utf-8') as file:  \n", "    examples = json.load(file)\n", "language='zh' # en or zh\n", "text=examples[0][language] # Text that needs to be segmented\n", "\n", "chunks=chunker(text,small_model,small_tokenizer,language)\n", "i=1\n", "for chunk in chunks:\n", "    print(f'Number {i}: ', chunk)\n", "    i+=1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Building prefix dict from the default dictionary ...\n", "Loading model from cache /tmp/jieba.cache\n", "Loading model cost 0.682 seconds.\n", "Prefix dict has been built successfully.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Answer: ID 13\n", "The program execution time is: 6.301790714263916 seconds.\n", "Number 1:  2023-08-01 10:47，正文：通气会现场 来源：湖南高院7月31日，湖南高院联合省司法厅召开新闻通气会。 湖南高院副院长杨翔，省委依法治省办成员、省司法厅党组成员、副厅长杨龙金通报2022年湖南省行政机关负责人出庭应诉有关情况，并发布5个典型案例。 2022年，全省经人民法院通知出庭的行政机关负责人出庭应诉率提升至96.5%。 杨翔介绍，从出庭应诉数量看，负责人出庭应诉意识普遍提升。 2022年，全省法院共发出行政机关负责人出庭应诉通知书4228份，行政机关负责人到庭应诉4018件。 行政机关负责人参加调查询问1117件，参与案件协调化解741件。 与2021年相比，行政机关负责人到庭应诉和参加调查询问等案件增加2802件。 从地区分布情况来看，全省各地经人民法院通知的行政机关负责人出庭应诉率均达到90%以上，较往年有明显提升。 2022年，从行政管理领域看，全省法院制发负责人出庭应诉通知书的案件所涉行政管理领域较为集中，自然资源、社会保障、公安、市场监管等部门负责人出庭应诉的案件数量较多。 从涉案行政行为看，被诉行为类型相对集中。 排名前五的行政行为类型依次为行政征收或征用类案件、行政确认类案件、不履行法定职责类案件、行政处罚类案件及行政登记类案件。 从出庭应诉负责人层级比例看，基层行政机关负责人出庭应诉占比较高。 县市区及乡镇负责人出庭应诉数量占全部出庭应诉案件数的80.8%。\n", "Number 2:  杨龙金介绍，为进一步加强和完善负责人出庭应诉制度建设，省委依法治省办、省法院、省司法厅联合印发《关于进一步推进行政机关负责人出庭应诉的工作方案》（以下简称《工作方案》），推动省政府出台《湖南省行政应诉工作规定》并召开全省行政应诉工作会议，依托府院联动，推动行政机关负责人出庭应诉工作有序开展。 湖南高院、省司法厅根据最高人民法院相关司法解释，在《工作方案》中统一了行政机关负责人出庭应诉的认定标准和计算方式，实现了全省负责人出庭应诉工作的标准化和规范化。 同时，推动将行政机关负责人出庭应诉情况纳入省绩效考核、平安建设、市域社会治理等考核指标体系，进一步压实出庭应诉主体责任。 《工作方案》还明确将行政机关负责人参与调解和解并实质化解争议的案件视为已履行出庭应诉义务，既提高了负责人出庭应诉的积极性，也有力维护了当事人合法权益，促进经济社会和谐稳定。\n"]}], "source": ["from lmchunker.modules import lumberchunker\n", "import json\n", "with open('data/example1.json', 'r', encoding='utf-8') as file:  \n", "    examples = json.load(file)\n", "### zhipuai needs to be installed: pip install zhipuai\n", "api_name='zhipuai' # The model name of the API that needs to be called\n", "api_configure={\"api_key\":\"\",\"model_name\":\"glm-4-air\"} # Need to fill in according to the model name\n", "language='zh' # en or zh\n", "text=examples[0][language] # Text that needs to be segmented\n", "dynamic_merge='no' # no or yes\n", "target_size=200 # If dynamic_merge='yes', then the chunk length value needs to be set\n", "chunks=lumberchunker(api_name,api_configure,language,text,dynamic_merge,target_size)\n", "i=1\n", "for chunk in chunks:\n", "    print(f'Number {i}: ', chunk)\n", "    i+=1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from transformers import AutoTokenizer, AutoModelForSeq2SeqLM\n", "import torch\n", "\n", "model_name = \"/d2/zhaoji<PERSON>/models/propositionizer-wiki-flan-t5-large\"\n", "device_id = 0  \n", "device = torch.device(f'cuda:{device_id}' if torch.cuda.is_available() and torch.cuda.device_count() > device_id else 'cpu')  \n", "tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "model = AutoModelForSeq2SeqLM.from_pretrained(model_name).to(device)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from lmchunker.modules.dense_x_retrieval import dense_x_retrieval\n", "import json\n", "with open('data/example1.json', 'r', encoding='utf-8') as file:  \n", "    examples = json.load(file)\n", "language='en' # At present, the model of this method is only applicable to English texts\n", "text=examples[0][language] # Text that needs to be segmented\n", "title=''\n", "section=''\n", "target_size=256  # This model can only accept a maximum length of 512, but longer texts tend to make it difficult for the model to extract effective information.\n", "chunks=dense_x_retrieval(tokenizer,model,text,title,section,target_size)\n", "i=1\n", "for chunk in chunks:\n", "    print(f'Number {i}: ', chunk)\n", "    i+=1"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}