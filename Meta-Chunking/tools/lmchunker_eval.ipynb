{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON>a-<PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/users/u2023000898/envs/mypypi/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "Sliding Window Attention is enabled but not implemented for `sdpa`; unexpected results may be encountered.\n"]}, {"data": {"text/plain": ["Qwen2ForCausalLM(\n", "  (model): Qwen2Model(\n", "    (embed_tokens): Embedding(151936, 1536)\n", "    (layers): ModuleList(\n", "      (0-27): 28 x Qwen2Decoder<PERSON>ayer(\n", "        (self_attn): Qwen2Attention(\n", "          (q_proj): Linear(in_features=1536, out_features=1536, bias=True)\n", "          (k_proj): Linear(in_features=1536, out_features=256, bias=True)\n", "          (v_proj): Linear(in_features=1536, out_features=256, bias=True)\n", "          (o_proj): Linear(in_features=1536, out_features=1536, bias=False)\n", "        )\n", "        (mlp): Qwen2MLP(\n", "          (gate_proj): Linear(in_features=1536, out_features=8960, bias=False)\n", "          (up_proj): Linear(in_features=1536, out_features=8960, bias=False)\n", "          (down_proj): Linear(in_features=8960, out_features=1536, bias=False)\n", "          (act_fn): SiLU()\n", "        )\n", "        (input_layernorm): Qwen2RMSNorm((1536,), eps=1e-06)\n", "        (post_attention_layernorm): Qwen2RMSNorm((1536,), eps=1e-06)\n", "      )\n", "    )\n", "    (norm): Qwen2RMSNorm((1536,), eps=1e-06)\n", "    (rotary_emb): Qwen2RotaryEmbedding()\n", "  )\n", "  (lm_head): Linear(in_features=1536, out_features=151936, bias=False)\n", ")"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch\n", "from transformers import AutoModelForCausalLM, AutoTokenizer\n", "model_name_or_path='/users/u2023000898/model/Qwen2.5-1.5B-Instruct' \n", "device_id = 1\n", "device = torch.device(f'cuda:{device_id}' if torch.cuda.is_available() and torch.cuda.device_count() > device_id else 'cpu')  \n", "small_tokenizer = AutoTokenizer.from_pretrained(model_name_or_path,trust_remote_code=True)  \n", "small_model = AutoModelForCausalLM.from_pretrained(model_name_or_path, trust_remote_code=True).to(device)  \n", "small_model.eval()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. PPL Chunking"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Building prefix dict from the default dictionary ...\n", "Loading model from cache /tmp/jieba.cache\n", "Loading model cost 0.651 seconds.\n", "Prefix dict has been built successfully.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["111 [[0, 1, 2], [3, 4], [5, 6], [7, 8, 9, 10], [11, 12, 13], [14, 15], [16]]\n", "The program execution time is: 1.4212067127227783 seconds.\n", "Number 1:  2023-08-01 10:47，正文：通气会现场 来源：湖南高院7月31日，湖南高院联合省司法厅召开新闻通气会。湖南高院副院长杨翔，省委依法治省办成员、省司法厅党组成员、副厅长杨龙金通报2022年湖南省行政机关负责人出庭应诉有关情况，并发布5个典型案例。2022年，全省经人民法院通知出庭的行政机关负责人出庭应诉率提升至96.5%。\n", "Number 2:  杨翔介绍，从出庭应诉数量看，负责人出庭应诉意识普遍提升。2022年，全省法院共发出行政机关负责人出庭应诉通知书4228份，行政机关负责人到庭应诉4018件。\n", "Number 3:  行政机关负责人参加调查询问1117件，参与案件协调化解741件。与2021年相比，行政机关负责人到庭应诉和参加调查询问等案件增加2802件。\n", "Number 4:  从地区分布情况来看，全省各地经人民法院通知的行政机关负责人出庭应诉率均达到90%以上，较往年有明显提升。2022年，从行政管理领域看，全省法院制发负责人出庭应诉通知书的案件所涉行政管理领域较为集中，自然资源、社会保障、公安、市场监管等部门负责人出庭应诉的案件数量较多。从涉案行政行为看，被诉行为类型相对集中。排名前五的行政行为类型依次为行政征收或征用类案件、行政确认类案件、不履行法定职责类案件、行政处罚类案件及行政登记类案件。\n", "Number 5:  从出庭应诉负责人层级比例看，基层行政机关负责人出庭应诉占比较高。县市区及乡镇负责人出庭应诉数量占全部出庭应诉案件数的80.8%。杨龙金介绍，为进一步加强和完善负责人出庭应诉制度建设，省委依法治省办、省法院、省司法厅联合印发《关于进一步推进行政机关负责人出庭应诉的工作方案》（以下简称《工作方案》），推动省政府出台《湖南省行政应诉工作规定》并召开全省行政应诉工作会议，依托府院联动，推动行政机关负责人出庭应诉工作有序开展。\n", "Number 6:  湖南高院、省司法厅根据最高人民法院相关司法解释，在《工作方案》中统一了行政机关负责人出庭应诉的认定标准和计算方式，实现了全省负责人出庭应诉工作的标准化和规范化。同时，推动将行政机关负责人出庭应诉情况纳入省绩效考核、平安建设、市域社会治理等考核指标体系，进一步压实出庭应诉主体责任。\n", "Number 7:  《工作方案》还明确将行政机关负责人参与调解和解并实质化解争议的案件视为已履行出庭应诉义务，既提高了负责人出庭应诉的积极性，也有力维护了当事人合法权益，促进经济社会和谐稳定。\n"]}], "source": ["from lmchunker.modules.ppl_chunking import llm_chunker_ppl\n", "import json\n", "with open('data/example1.json', 'r', encoding='utf-8') as file:  \n", "    examples = json.load(file)\n", "\n", "language='zh' # en or zh\n", "text=examples[0][language] # Text that needs to be segmented\n", "threshold=0  # The threshold for controlling PPL Chunking is inversely proportional to the chunk length; the smaller the threshold, the shorter the chunk length.\n", "dynamic_merge='no' # no or yes\n", "target_size=200 # If dynamic_merge='yes', then the chunk length value needs to be set\n", "\n", "# When dealing with longer documents (exceeding 4096 characters), it is recommended to use KV caching to optimize GPU memory usage. \n", "# The batch_size refers to the length of a single document processed at a time. \n", "# The max_txt_size represents the total context length that can be considered or the maximum length that the GPU memory can accommodate.\n", "batch_size=4096\n", "max_txt_size=9000\n", "\n", "chunks=llm_chunker_ppl(text,small_model,small_tokenizer,threshold,language,dynamic_merge=dynamic_merge,target_size=target_size,batch_size=batch_size,max_txt_size=max_txt_size)  \n", "i=1\n", "for chunk in chunks:\n", "    print(f'Number {i}: ', chunk)\n", "    i+=1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. <PERSON><PERSON> Chun<PERSON>"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The program execution time is: 1.1948745250701904 seconds.\n", "Number 1:  2023-08-01 10:47，正文：通气会现场 来源：湖南高院7月31日，湖南高院联合省司法厅召开新闻通气会。湖南高院副院长杨翔，省委依法治省办成员、省司法厅党组成员、副厅长杨龙金通报2022年湖南省行政机关负责人出庭应诉有关情况，并发布5个典型案例。2022年，全省经人民法院通知出庭的行政机关负责人出庭应诉率提升至96.5%。杨翔介绍，从出庭应诉数量看，负责人出庭应诉意识普遍提升。2022年，全省法院共发出行政机关负责人出庭应诉通知书4228份，行政机关负责人到庭应诉4018件。\n", "Number 2:  行政机关负责人参加调查询问1117件，参与案件协调化解741件。与2021年相比，行政机关负责人到庭应诉和参加调查询问等案件增加2802件。从地区分布情况来看，全省各地经人民法院通知的行政机关负责人出庭应诉率均达到90%以上，较往年有明显提升。2022年，从行政管理领域看，全省法院制发负责人出庭应诉通知书的案件所涉行政管理领域较为集中，自然资源、社会保障、公安、市场监管等部门负责人出庭应诉的案件数量较多。从涉案行政行为看，被诉行为类型相对集中。排名前五的行政行为类型依次为行政征收或征用类案件、行政确认类案件、不履行法定职责类案件、行政处罚类案件及行政登记类案件。\n", "Number 3:  从出庭应诉负责人层级比例看，基层行政机关负责人出庭应诉占比较高。县市区及乡镇负责人出庭应诉数量占全部出庭应诉案件数的80.8%。杨龙金介绍，为进一步加强和完善负责人出庭应诉制度建设，省委依法治省办、省法院、省司法厅联合印发《关于进一步推进行政机关负责人出庭应诉的工作方案》（以下简称《工作方案》），推动省政府出台《湖南省行政应诉工作规定》并召开全省行政应诉工作会议，依托府院联动，推动行政机关负责人出庭应诉工作有序开展。湖南高院、省司法厅根据最高人民法院相关司法解释，在《工作方案》中统一了行政机关负责人出庭应诉的认定标准和计算方式，实现了全省负责人出庭应诉工作的标准化和规范化。\n", "Number 4:  同时，推动将行政机关负责人出庭应诉情况纳入省绩效考核、平安建设、市域社会治理等考核指标体系，进一步压实出庭应诉主体责任。《工作方案》还明确将行政机关负责人参与调解和解并实质化解争议的案件视为已履行出庭应诉义务，既提高了负责人出庭应诉的积极性，也有力维护了当事人合法权益，促进经济社会和谐稳定。\n"]}], "source": ["from lmchunker.modules.margin_sampling_chunking import llm_chunker_ms\n", "import json\n", "with open('data/example1.json', 'r', encoding='utf-8') as file:  \n", "    examples = json.load(file)\n", "\n", "language='zh' # en or zh\n", "text=examples[0][language] # Text that needs to be segmented\n", "\n", "dynamic_merge='yes' # no or yes\n", "target_size=300 # If dynamic_merge='yes', then the chunk length value needs to be set\n", "\n", "\n", "chunks=llm_chunker_ms(text,small_model,small_tokenizer,language,dynamic_merge,target_size)  \n", "i=1\n", "for chunk in chunks:\n", "    print(f'Number {i}: ', chunk)\n", "    i+=1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## lumberchunker"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import json\n", "with open('data/example1.json', 'r', encoding='utf-8') as file:  \n", "    examples = json.load(file)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Answer: ID 13\n", "The program execution time is: 2.769681930541992 seconds.\n", "Number 1:  2023-08-01 10:47，正文：通气会现场 来源：湖南高院7月31日，湖南高院联合省司法厅召开新闻通气会。 湖南高院副院长杨翔，省委依法治省办成员、省司法厅党组成员、副厅长杨龙金通报2022年湖南省行政机关负责人出庭应诉有关情况，并发布5个典型案例。 2022年，全省经人民法院通知出庭的行政机关负责人出庭应诉率提升至96.5%。 杨翔介绍，从出庭应诉数量看，负责人出庭应诉意识普遍提升。 2022年，全省法院共发出行政机关负责人出庭应诉通知书4228份，行政机关负责人到庭应诉4018件。 行政机关负责人参加调查询问1117件，参与案件协调化解741件。 与2021年相比，行政机关负责人到庭应诉和参加调查询问等案件增加2802件。 从地区分布情况来看，全省各地经人民法院通知的行政机关负责人出庭应诉率均达到90%以上，较往年有明显提升。 2022年，从行政管理领域看，全省法院制发负责人出庭应诉通知书的案件所涉行政管理领域较为集中，自然资源、社会保障、公安、市场监管等部门负责人出庭应诉的案件数量较多。 从涉案行政行为看，被诉行为类型相对集中。 排名前五的行政行为类型依次为行政征收或征用类案件、行政确认类案件、不履行法定职责类案件、行政处罚类案件及行政登记类案件。 从出庭应诉负责人层级比例看，基层行政机关负责人出庭应诉占比较高。 县市区及乡镇负责人出庭应诉数量占全部出庭应诉案件数的80.8%。\n", "Number 2:  杨龙金介绍，为进一步加强和完善负责人出庭应诉制度建设，省委依法治省办、省法院、省司法厅联合印发《关于进一步推进行政机关负责人出庭应诉的工作方案》（以下简称《工作方案》），推动省政府出台《湖南省行政应诉工作规定》并召开全省行政应诉工作会议，依托府院联动，推动行政机关负责人出庭应诉工作有序开展。 湖南高院、省司法厅根据最高人民法院相关司法解释，在《工作方案》中统一了行政机关负责人出庭应诉的认定标准和计算方式，实现了全省负责人出庭应诉工作的标准化和规范化。 同时，推动将行政机关负责人出庭应诉情况纳入省绩效考核、平安建设、市域社会治理等考核指标体系，进一步压实出庭应诉主体责任。 《工作方案》还明确将行政机关负责人参与调解和解并实质化解争议的案件视为已履行出庭应诉义务，既提高了负责人出庭应诉的积极性，也有力维护了当事人合法权益，促进经济社会和谐稳定。\n"]}], "source": ["from lmchunker.modules.lumberchunker import lumberchunker\n", "api_name='zhipuai' # The model name of the API that needs to be called\n", "api_configure={\"api_key\":\"\",\"model_name\":\"glm-4-air\"} # Need to fill in according to the model name\n", "language='zh' # en or zh\n", "text=examples[0][language] # Text that needs to be segmented\n", "dynamic_merge='no' # no or yes\n", "target_size=200 # If dynamic_merge='yes', then the chunk length value needs to be set\n", "chunks=lumberchunker(api_name,api_configure,language,text,dynamic_merge,target_size)\n", "i=1\n", "for chunk in chunks:\n", "    print(f'Number {i}: ', chunk)\n", "    i+=1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## lumberchunker_margin_sampling"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # 多卡加载模型\n", "# from transformers import AutoModelForCausalLM, AutoTokenizer\n", "# model_name_or_path= '/mnt/data102_d2/huggingface/models/Qwen2-7B-Instruct'     \n", "# device_map = \"auto\"\n", "# small_tokenizer = AutoTokenizer.from_pretrained(model_name_or_path,trust_remote_code=True)  \n", "# small_model = AutoModelForCausalLM.from_pretrained(model_name_or_path, trust_remote_code=True,device_map=device_map) \n", "# small_model.eval()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 单卡加载模型\n", "from transformers import AutoModelForCausalLM, AutoTokenizer\n", "import torch\n", "model_name_or_path= '/mnt/data102_d2/huggingface/models/Qwen2-7B-Instruct'  \n", "device_id = 7  \n", "device = torch.device(f'cuda:{device_id}' if torch.cuda.is_available() and torch.cuda.device_count() > device_id else 'cpu')  \n", "small_tokenizer = AutoTokenizer.from_pretrained(model_name_or_path,trust_remote_code=True)  \n", "small_model = AutoModelForCausalLM.from_pretrained(model_name_or_path, trust_remote_code=True).to(device)   \n", "small_model.eval()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import json\n", "with open('data/example1.json', 'r', encoding='utf-8') as file:  \n", "    examples = json.load(file)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The program execution time is: 0.27474045753479004 seconds.\n", "Number 1:  2023-08-01 10:47，正文：通气会现场 来源：湖南高院7月31日，湖南高院联合省司法厅召开新闻通气会。\n", "Number 2:  湖南高院副院长杨翔，省委依法治省办成员、省司法厅党组成员、副厅长杨龙金通报2022年湖南省行政机关负责人出庭应诉有关情况，并发布5个典型案例。\n", "Number 3:  2022年，全省经人民法院通知出庭的行政机关负责人出庭应诉率提升至96.5%。 杨翔介绍，从出庭应诉数量看，负责人出庭应诉意识普遍提升。 2022年，全省法院共发出行政机关负责人出庭应诉通知书4228份，行政机关负责人到庭应诉4018件。 行政机关负责人参加调查询问1117件，参与案件协调化解741件。 与2021年相比，行政机关负责人到庭应诉和参加调查询问等案件增加2802件。\n", "Number 4:  从地区分布情况来看，全省各地经人民法院通知的行政机关负责人出庭应诉率均达到90%以上，较往年有明显提升。 2022年，从行政管理领域看，全省法院制发负责人出庭应诉通知书的案件所涉行政管理领域较为集中，自然资源、社会保障、公安、市场监管等部门负责人出庭应诉的案件数量较多。 从涉案行政行为看，被诉行为类型相对集中。 排名前五的行政行为类型依次为行政征收或征用类案件、行政确认类案件、不履行法定职责类案件、行政处罚类案件及行政登记类案件。 从出庭应诉负责人层级比例看，基层行政机关负责人出庭应诉占比较高。\n", "Number 5:  县市区及乡镇负责人出庭应诉数量占全部出庭应诉案件数的80.8%。 杨龙金介绍，为进一步加强和完善负责人出庭应诉制度建设，省委依法治省办、省法院、省司法厅联合印发《关于进一步推进行政机关负责人出庭应诉的工作方案》（以下简称《工作方案》），推动省政府出台《湖南省行政应诉工作规定》并召开全省行政应诉工作会议，依托府院联动，推动行政机关负责人出庭应诉工作有序开展。 湖南高院、省司法厅根据最高人民法院相关司法解释，在《工作方案》中统一了行政机关负责人出庭应诉的认定标准和计算方式，实现了全省负责人出庭应诉工作的标准化和规范化。 同时，推动将行政机关负责人出庭应诉情况纳入省绩效考核、平安建设、市域社会治理等考核指标体系，进一步压实出庭应诉主体责任。 《工作方案》还明确将行政机关负责人参与调解和解并实质化解争议的案件视为已履行出庭应诉义务，既提高了负责人出庭应诉的积极性，也有力维护了当事人合法权益，促进经济社会和谐稳定。\n"]}], "source": ["from lmchunker.modules.lumberchunker_margin_sampling import lumberchunker_ms\n", "language='zh' # en or zh\n", "text=examples[0][language] # Text that needs to be segmented\n", "dynamic_merge='no' # no or yes\n", "target_size=200 # If dynamic_merge='yes', then the chunk length value needs to be set\n", "chunks=lumberchunker_ms(small_tokenizer,small_model,language,text,dynamic_merge,target_size)\n", "i=1\n", "for chunk in chunks:\n", "    print(f'Number {i}: ', chunk)\n", "    i+=1"]}, {"cell_type": "markdown", "metadata": {}, "source": [" ## dense_x_retrieval"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/d2/zhao<PERSON><PERSON>/envs/judge/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from transformers import AutoTokenizer, AutoModelForSeq2SeqLM\n", "import torch\n", "\n", "model_name = \"/d2/zhaoji<PERSON>/models/propositionizer-wiki-flan-t5-large\"\n", "device_id = 0  \n", "device = torch.device(f'cuda:{device_id}' if torch.cuda.is_available() and torch.cuda.device_count() > device_id else 'cpu')  \n", "tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "model = AutoModelForSeq2SeqLM.from_pretrained(model_name).to(device)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "with open('data/example1.json', 'r', encoding='utf-8') as file:  \n", "    examples = json.load(file)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Title: . Section: . Content:  <PERSON><PERSON><PERSON> of Lotharingia\n", "<PERSON><PERSON><PERSON> was the mistress, and later the wife, of <PERSON><PERSON><PERSON> of Lotharingia. Biography\n", "<PERSON><PERSON><PERSON>'s family origin is uncertain. The prolific 19th-century French writer <PERSON> suggested that <PERSON><PERSON><PERSON> was of noble Gallo-Roman descent, sister of <PERSON><PERSON><PERSON><PERSON><PERSON>, the bishop of Trier, and niece of <PERSON><PERSON>, archbishop of Cologne. However, these suggestions are not supported by any evidence, and more recent studies have instead suggested she was of relatively undistinguished social origins, though still from an aristocratic milieu.\n", "[ERROR] Failed to parse output text as JSON.\n", "Title: . Section: . Content: The Vita Sancti Deicoli states that <PERSON><PERSON><PERSON> was related to <PERSON><PERSON><PERSON>, Count of Nordgau (included Strasbourg) and the family of <PERSON><PERSON><PERSON><PERSON>, though this is a late 10th-century source and so may not be entirely reliable on this question.In 855 the Carolingian king <PERSON><PERSON> married <PERSON><PERSON><PERSON><PERSON>, a Carolingian aristocrat and the daughter of <PERSON><PERSON><PERSON> the Elder. The marriage was arranged by <PERSON><PERSON>'s father <PERSON><PERSON> for political reasons. It is very probable that <PERSON><PERSON><PERSON> was already <PERSON><PERSON>'s mistress at this time.<PERSON><PERSON><PERSON><PERSON> was allegedly not capable of bearing children and <PERSON><PERSON>'s reign was chiefly occupied by his efforts to obtain an annulment of their marriage, and his relations with his uncles <PERSON> the <PERSON> and <PERSON> the <PERSON> were influenced by his desire to obtain their support for this endeavour. <PERSON><PERSON><PERSON>, whose desire for annulment was arguably prompted by his affection for <PERSON><PERSON><PERSON>, put away <PERSON><PERSON><PERSON><PERSON>.\n", "Title: . Section: . Content: However, <PERSON><PERSON><PERSON> took up arms on his sister's behalf, and after she had submitted successfully to the ordeal of water, <PERSON><PERSON><PERSON> was compelled to restore her in 858. Still pursuing his purpose, he won the support of his brother, Emperor <PERSON>, by a cession of lands and obtained the consent of the local clergy to the annulment and to his marriage with <PERSON><PERSON><PERSON>, which took place in 862. However, <PERSON> <PERSON> was suspicious of this and sent legates to investigate at the Council of Metz in 863. The Council found in favour of <PERSON><PERSON><PERSON>'s divorce, which led to rumours that the papal legates may have bribed and thus meant that <PERSON> order <PERSON><PERSON><PERSON> to take Teutberga back or face excommunication. With the support of <PERSON> the <PERSON>ld and <PERSON> the German, <PERSON><PERSON><PERSON><PERSON> appealed the annulment to <PERSON>. <PERSON> refused to recognize the annulment and excommunicated <PERSON><PERSON><PERSON> in 866, forcing <PERSON><PERSON><PERSON> to abandon <PERSON><PERSON><PERSON> in favour of <PERSON><PERSON><PERSON><PERSON>.\n", "Title: . Section: . Content: <PERSON><PERSON><PERSON> accepted this begrudgingly for a time, but shortly afterward at the end of 867 <PERSON> <PERSON> died. Thus, <PERSON><PERSON><PERSON> began to seek the permission of the newly appointed Pope <PERSON> to again put <PERSON><PERSON><PERSON><PERSON> aside and marry <PERSON><PERSON><PERSON>, riding to Rome to speak with him on the matter in 869. However, on his way home, <PERSON><PERSON><PERSON> died. Children\n", "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> <PERSON> had some sons and probably three daughters, all of whom were declared illegitimate:\n", "\n", "<PERSON> (c. 855–895), Duke of Alsace (867–885)\n", "<PERSON><PERSON><PERSON> (c. 865–908), who in 883 married <PERSON>, the Viking leader ruling in Frisia, who was murdered in 885\n", "<PERSON><PERSON> (c. 863–925), who married <PERSON><PERSON><PERSON> of Arles (c. 854–895), count of Arles, nephew of <PERSON><PERSON><PERSON><PERSON>. They had two sons, <PERSON> of Italy and <PERSON><PERSON> of Tuscany.\n", "Title: . Section: . Content: After <PERSON><PERSON><PERSON>'s death, between 895 and 898 she married <PERSON><PERSON> of Tuscany (c. 875–915) They had at least three children: <PERSON>, who succeeded his father as count and duke of Lucca and margrave of Tuscany, <PERSON> succeeded his brother in 929, but lost the titles in 931 to his half-brother <PERSON><PERSON> of Tuscany, and <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> (d. 90?) <PERSON><PERSON> (d. c.879)\n", "Number 1:  The Vita Sancti Deicoli states that <PERSON><PERSON><PERSON> was related to <PERSON><PERSON><PERSON>, Count of Nordgau, and the family of Etichonids.\n", "Number 2:  <PERSON><PERSON><PERSON> was the Count of Nordgau.\n", "Number 3:  Nordgau included Strasbourg.\n", "Number 4:  The Vita Sancti Deicoli is a late 10th-century source.\n", "Number 5:  The Vita Sancti Deicoli may not be entirely reliable on this question.\n", "Number 6:  In 855, <PERSON><PERSON> married <PERSON><PERSON><PERSON><PERSON>.\n", "Number 7:  <PERSON><PERSON><PERSON><PERSON> was a Carolingian aristocrat.\n", "Number 8:  <PERSON><PERSON><PERSON><PERSON> was the daughter of <PERSON><PERSON><PERSON> the Elder.\n", "Number 9:  The marriage between <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> was arranged by <PERSON><PERSON> for political reasons.\n", "Number 10:  It is probable that <PERSON><PERSON><PERSON> was already <PERSON><PERSON>'s mistress at this time.\n", "Number 11:  <PERSON><PERSON><PERSON><PERSON> was allegedly not capable of bearing children.\n", "Number 12:  <PERSON><PERSON>'s reign was chiefly occupied by his efforts to obtain an annulment of their marriage.\n", "Number 13:  <PERSON><PERSON>'s relations with his uncles <PERSON> the <PERSON> and <PERSON> the German were influenced by his desire to obtain their support for their annulment.\n", "Number 14:  <PERSON><PERSON><PERSON>'s desire for annulment was arguably prompted by his affection for <PERSON><PERSON><PERSON>.\n", "Number 15:  <PERSON><PERSON><PERSON> put away <PERSON><PERSON><PERSON><PERSON>.\n", "Number 16:  <PERSON><PERSON><PERSON> took up arms on his sister's behalf.\n", "Number 17:  <PERSON><PERSON><PERSON>'s sister submitted successfully to the ordeal of water.\n", "Number 18:  <PERSON><PERSON><PERSON> was compelled to restore <PERSON><PERSON><PERSON>'s sister in 858.\n", "Number 19:  <PERSON><PERSON><PERSON> won the support of his brother, Emperor <PERSON>, by a cession of lands.\n", "Number 20:  <PERSON><PERSON><PERSON> obtained the consent of the local clergy to the annulment and to his marriage with <PERSON><PERSON><PERSON>.\n", "Number 21:  The annulment and marriage with <PERSON><PERSON><PERSON> took place in 862.\n", "Number 22:  <PERSON> I was suspicious of <PERSON><PERSON><PERSON>'s intentions.\n", "Number 23:  <PERSON> I sent legates to investigate at the Council of Metz in 863.\n", "Number 24:  The Council of Metz found in favour of <PERSON><PERSON><PERSON>'s divorce.\n", "Number 25:  R<PERSON><PERSON> suggested that the papal legates may have bribed.\n", "Number 26:  <PERSON> I ordered <PERSON><PERSON><PERSON> to take Teutberga back or face excommunication.\n", "Number 27:  <PERSON> the Bald and <PERSON> the German supported <PERSON><PERSON><PERSON><PERSON>.\n", "Number 28:  <PERSON><PERSON><PERSON><PERSON> appealed the annulment to <PERSON>.\n", "Number 29:  <PERSON> refused to recognize the annulment.\n", "Number 30:  <PERSON> <PERSON> excommunicated <PERSON><PERSON><PERSON> in 866.\n", "Number 31:  <PERSON><PERSON><PERSON> was forced to abandon Waldrada in favour of <PERSON><PERSON><PERSON><PERSON>.\n", "Number 32:  <PERSON><PERSON><PERSON> accepted <PERSON> <PERSON>'s marriage proposal begrudgingly for a time.\n", "Number 33:  <PERSON> died at the end of 867.\n", "Number 34:  <PERSON><PERSON><PERSON> began to seek the permission of Pope <PERSON> to marry <PERSON><PERSON><PERSON>.\n", "Number 35:  <PERSON> was newly appointed.\n", "Number 36:  <PERSON><PERSON><PERSON> rode to Rome to speak with Pope <PERSON> on the matter in 869.\n", "Number 37:  On <PERSON><PERSON><PERSON>'s way home, he died.\n", "Number 38:  <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> had some sons and probably three daughters.\n", "Number 39:  All of the children were declared illegitimate.\n", "Number 40:  <PERSON> was the Duke of Alsace from 855 to 895.\n", "Number 41:  <PERSON><PERSON><PERSON> was the Duke of Alsace from 867 to 885.\n", "Number 42:  <PERSON><PERSON><PERSON> married <PERSON> in 883.\n", "Number 43:  <PERSON> was the Viking leader ruling in Frisia.\n", "Number 44:  <PERSON> was murdered in 885.\n", "Number 45:  <PERSON><PERSON> was the daughter of <PERSON> and <PERSON><PERSON><PERSON>.\n", "Number 46:  <PERSON><PERSON> was born around 863 to 925.\n", "Number 47:  <PERSON><PERSON> married <PERSON><PERSON><PERSON> of Arles.\n", "Number 48:  <PERSON><PERSON><PERSON> of Arles was the count of Arles from 854 to 895.\n", "Number 49:  <PERSON><PERSON><PERSON> of Arles was the nephew of <PERSON><PERSON><PERSON><PERSON>.\n", "Number 50:  <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> of Arles had two sons, <PERSON> of Italy and <PERSON><PERSON> of Tuscany.\n", "Number 51:  <PERSON><PERSON><PERSON> died.\n", "Number 52:  Between 895 and 898, <PERSON><PERSON><PERSON> married <PERSON><PERSON> of Tuscany.\n", "Number 53:  <PERSON><PERSON> of Tuscany lived between 875 and 915.\n", "Number 54:  <PERSON><PERSON><PERSON> and <PERSON><PERSON> of Tuscany had at least three children.\n", "Number 55:  <PERSON> succeeded his father as count and duke of Lucca and margrave of Tuscany.\n", "Number 56:  <PERSON> succeeded his brother in 929.\n", "Number 57:  <PERSON> lost the titles in 931 to his half-brother <PERSON><PERSON> of Tuscany.\n", "Number 58:  <PERSON><PERSON><PERSON><PERSON><PERSON> was one of the children.\n", "Number 59:  <PERSON><PERSON><PERSON><PERSON><PERSON> died in 90.\n", "Number 60:  <PERSON><PERSON> was one of the children.\n", "Number 61:  <PERSON><PERSON> died around 879.\n"]}], "source": ["from lmchunker.modules.dense_x_retrieval import dense_x_retrieval\n", "language='en' # At present, the model of this method is only applicable to English texts\n", "text=examples[0][language] # Text that needs to be segmented\n", "title=''\n", "section=''\n", "target_size=256  # This model can only accept a maximum length of 512, but longer texts tend to make it difficult for the model to extract effective information.\n", "chunks=dense_x_retrieval(tokenizer,model,text,title,section,target_size)\n", "i=1\n", "for chunk in chunks:\n", "    print(f'Number {i}: ', chunk)\n", "    i+=1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Examples of using lmchunker package"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/d2/zhaoji<PERSON>/envs/mypypi/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"data": {"text/plain": ["Qwen2ForCausalLM(\n", "  (model): Qwen2Model(\n", "    (embed_tokens): Embedding(151936, 1536)\n", "    (layers): ModuleList(\n", "      (0-27): 28 x Qwen2Decoder<PERSON>ayer(\n", "        (self_attn): Qwen2SdpaAttention(\n", "          (q_proj): Linear(in_features=1536, out_features=1536, bias=True)\n", "          (k_proj): Linear(in_features=1536, out_features=256, bias=True)\n", "          (v_proj): Linear(in_features=1536, out_features=256, bias=True)\n", "          (o_proj): Linear(in_features=1536, out_features=1536, bias=False)\n", "          (rotary_emb): Qwen2RotaryEmbedding()\n", "        )\n", "        (mlp): Qwen2MLP(\n", "          (gate_proj): Linear(in_features=1536, out_features=8960, bias=False)\n", "          (up_proj): Linear(in_features=1536, out_features=8960, bias=False)\n", "          (down_proj): Linear(in_features=8960, out_features=1536, bias=False)\n", "          (act_fn): SiLU()\n", "        )\n", "        (input_layernorm): Qwen2RMSNorm((1536,), eps=1e-06)\n", "        (post_attention_layernorm): Qwen2RMSNorm((1536,), eps=1e-06)\n", "      )\n", "    )\n", "    (norm): Qwen2RMSNorm((1536,), eps=1e-06)\n", "    (rotary_emb): Qwen2RotaryEmbedding()\n", "  )\n", "  (lm_head): Linear(in_features=1536, out_features=151936, bias=False)\n", ")"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch\n", "from transformers import AutoModelForCausalLM, AutoTokenizer\n", "model_name_or_path='/mnt/data102_d2/huggingface/models/Qwen2-1.5B-Instruct' \n", "device_id = 6   \n", "device = torch.device(f'cuda:{device_id}' if torch.cuda.is_available() and torch.cuda.device_count() > device_id else 'cpu')  \n", "small_tokenizer = AutoTokenizer.from_pretrained(model_name_or_path,trust_remote_code=True)  \n", "small_model = AutoModelForCausalLM.from_pretrained(model_name_or_path, trust_remote_code=True).to(device)  \n", "small_model.eval()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["111 [[0, 1, 2], [3, 4], [5, 6, 7], [8, 9, 10], [11, 12], [13, 14, 15], [16]]\n", "The program execution time is: 0.10892772674560547 seconds.\n", "Number 1:  2023-08-01 10:47，正文：通气会现场 来源：湖南高院7月31日，湖南高院联合省司法厅召开新闻通气会。湖南高院副院长杨翔，省委依法治省办成员、省司法厅党组成员、副厅长杨龙金通报2022年湖南省行政机关负责人出庭应诉有关情况，并发布5个典型案例。2022年，全省经人民法院通知出庭的行政机关负责人出庭应诉率提升至96.5%。\n", "Number 2:  杨翔介绍，从出庭应诉数量看，负责人出庭应诉意识普遍提升。2022年，全省法院共发出行政机关负责人出庭应诉通知书4228份，行政机关负责人到庭应诉4018件。\n", "Number 3:  行政机关负责人参加调查询问1117件，参与案件协调化解741件。与2021年相比，行政机关负责人到庭应诉和参加调查询问等案件增加2802件。从地区分布情况来看，全省各地经人民法院通知的行政机关负责人出庭应诉率均达到90%以上，较往年有明显提升。\n", "Number 4:  2022年，从行政管理领域看，全省法院制发负责人出庭应诉通知书的案件所涉行政管理领域较为集中，自然资源、社会保障、公安、市场监管等部门负责人出庭应诉的案件数量较多。从涉案行政行为看，被诉行为类型相对集中。排名前五的行政行为类型依次为行政征收或征用类案件、行政确认类案件、不履行法定职责类案件、行政处罚类案件及行政登记类案件。\n", "Number 5:  从出庭应诉负责人层级比例看，基层行政机关负责人出庭应诉占比较高。县市区及乡镇负责人出庭应诉数量占全部出庭应诉案件数的80.8%。\n", "Number 6:  杨龙金介绍，为进一步加强和完善负责人出庭应诉制度建设，省委依法治省办、省法院、省司法厅联合印发《关于进一步推进行政机关负责人出庭应诉的工作方案》（以下简称《工作方案》），推动省政府出台《湖南省行政应诉工作规定》并召开全省行政应诉工作会议，依托府院联动，推动行政机关负责人出庭应诉工作有序开展。湖南高院、省司法厅根据最高人民法院相关司法解释，在《工作方案》中统一了行政机关负责人出庭应诉的认定标准和计算方式，实现了全省负责人出庭应诉工作的标准化和规范化。同时，推动将行政机关负责人出庭应诉情况纳入省绩效考核、平安建设、市域社会治理等考核指标体系，进一步压实出庭应诉主体责任。\n", "Number 7:  《工作方案》还明确将行政机关负责人参与调解和解并实质化解争议的案件视为已履行出庭应诉义务，既提高了负责人出庭应诉的积极性，也有力维护了当事人合法权益，促进经济社会和谐稳定。\n"]}], "source": ["from lmchunker import chunker\n", "import json\n", "with open('data/example1.json', 'r', encoding='utf-8') as file:  \n", "    examples = json.load(file)\n", "language='zh' # en or zh\n", "text=examples[0][language] # Text that needs to be segmented\n", "\n", "chunks=chunker(text,small_model,small_tokenizer,language)\n", "i=1\n", "for chunk in chunks:\n", "    print(f'Number {i}: ', chunk)\n", "    i+=1"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Building prefix dict from the default dictionary ...\n", "Loading model from cache /tmp/jieba.cache\n", "Loading model cost 1.714 seconds.\n", "Prefix dict has been built successfully.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Answer: ID 13\n", "The program execution time is: 4.001843690872192 seconds.\n", "Number 1:  2023-08-01 10:47，正文：通气会现场 来源：湖南高院7月31日，湖南高院联合省司法厅召开新闻通气会。 湖南高院副院长杨翔，省委依法治省办成员、省司法厅党组成员、副厅长杨龙金通报2022年湖南省行政机关负责人出庭应诉有关情况，并发布5个典型案例。 2022年，全省经人民法院通知出庭的行政机关负责人出庭应诉率提升至96.5%。 杨翔介绍，从出庭应诉数量看，负责人出庭应诉意识普遍提升。 2022年，全省法院共发出行政机关负责人出庭应诉通知书4228份，行政机关负责人到庭应诉4018件。 行政机关负责人参加调查询问1117件，参与案件协调化解741件。 与2021年相比，行政机关负责人到庭应诉和参加调查询问等案件增加2802件。 从地区分布情况来看，全省各地经人民法院通知的行政机关负责人出庭应诉率均达到90%以上，较往年有明显提升。 2022年，从行政管理领域看，全省法院制发负责人出庭应诉通知书的案件所涉行政管理领域较为集中，自然资源、社会保障、公安、市场监管等部门负责人出庭应诉的案件数量较多。 从涉案行政行为看，被诉行为类型相对集中。 排名前五的行政行为类型依次为行政征收或征用类案件、行政确认类案件、不履行法定职责类案件、行政处罚类案件及行政登记类案件。 从出庭应诉负责人层级比例看，基层行政机关负责人出庭应诉占比较高。 县市区及乡镇负责人出庭应诉数量占全部出庭应诉案件数的80.8%。\n", "Number 2:  杨龙金介绍，为进一步加强和完善负责人出庭应诉制度建设，省委依法治省办、省法院、省司法厅联合印发《关于进一步推进行政机关负责人出庭应诉的工作方案》（以下简称《工作方案》），推动省政府出台《湖南省行政应诉工作规定》并召开全省行政应诉工作会议，依托府院联动，推动行政机关负责人出庭应诉工作有序开展。 湖南高院、省司法厅根据最高人民法院相关司法解释，在《工作方案》中统一了行政机关负责人出庭应诉的认定标准和计算方式，实现了全省负责人出庭应诉工作的标准化和规范化。 同时，推动将行政机关负责人出庭应诉情况纳入省绩效考核、平安建设、市域社会治理等考核指标体系，进一步压实出庭应诉主体责任。 《工作方案》还明确将行政机关负责人参与调解和解并实质化解争议的案件视为已履行出庭应诉义务，既提高了负责人出庭应诉的积极性，也有力维护了当事人合法权益，促进经济社会和谐稳定。\n"]}], "source": ["from lmchunker.modules import lumberchunker\n", "import json\n", "with open('data/example1.json', 'r', encoding='utf-8') as file:  \n", "    examples = json.load(file)\n", "### zhipuai needs to be installed: pip install zhipuai\n", "api_name='zhipuai' # The model name of the API that needs to be called\n", "api_configure={\"api_key\":\"your_api_key\",\"model_name\":\"glm-4-0520\"} # Need to fill in according to the model name\n", "language='zh' # en or zh\n", "text=examples[0][language] # Text that needs to be segmented\n", "dynamic_merge='no' # no or yes\n", "target_size=200 # If dynamic_merge='yes', then the chunk length value needs to be set\n", "chunks=lumberchunker(api_name,api_configure,language,text,dynamic_merge,target_size)\n", "i=1\n", "for chunk in chunks:\n", "    print(f'Number {i}: ', chunk)\n", "    i+=1"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from transformers import AutoTokenizer, AutoModelForSeq2SeqLM\n", "import torch\n", "\n", "model_name = \"/d2/zhaoji<PERSON>/models/propositionizer-wiki-flan-t5-large\"\n", "device_id = 0  \n", "device = torch.device(f'cuda:{device_id}' if torch.cuda.is_available() and torch.cuda.device_count() > device_id else 'cpu')  \n", "tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "model = AutoModelForSeq2SeqLM.from_pretrained(model_name).to(device)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Title: . Section: . Content:  <PERSON><PERSON><PERSON> of Lotharingia\n", "<PERSON><PERSON><PERSON> was the mistress, and later the wife, of <PERSON><PERSON><PERSON> of Lotharingia. Biography\n", "<PERSON><PERSON><PERSON>'s family origin is uncertain. The prolific 19th-century French writer <PERSON> suggested that <PERSON><PERSON><PERSON> was of noble Gallo-Roman descent, sister of <PERSON><PERSON><PERSON><PERSON><PERSON>, the bishop of Trier, and niece of <PERSON><PERSON>, archbishop of Cologne. However, these suggestions are not supported by any evidence, and more recent studies have instead suggested she was of relatively undistinguished social origins, though still from an aristocratic milieu.\n", "[ERROR] Failed to parse output text as JSON.\n", "Title: . Section: . Content: The Vita Sancti Deicoli states that <PERSON><PERSON><PERSON> was related to <PERSON><PERSON><PERSON>, Count of Nordgau (included Strasbourg) and the family of <PERSON><PERSON><PERSON><PERSON>, though this is a late 10th-century source and so may not be entirely reliable on this question.In 855 the Carolingian king <PERSON><PERSON> married <PERSON><PERSON><PERSON><PERSON>, a Carolingian aristocrat and the daughter of <PERSON><PERSON><PERSON> the Elder. The marriage was arranged by <PERSON><PERSON>'s father <PERSON><PERSON> for political reasons. It is very probable that <PERSON><PERSON><PERSON> was already <PERSON><PERSON>'s mistress at this time.<PERSON><PERSON><PERSON><PERSON> was allegedly not capable of bearing children and <PERSON><PERSON>'s reign was chiefly occupied by his efforts to obtain an annulment of their marriage, and his relations with his uncles <PERSON> the <PERSON> and <PERSON> the <PERSON> were influenced by his desire to obtain their support for this endeavour. <PERSON><PERSON><PERSON>, whose desire for annulment was arguably prompted by his affection for <PERSON><PERSON><PERSON>, put away <PERSON><PERSON><PERSON><PERSON>.\n", "Title: . Section: . Content: However, <PERSON><PERSON><PERSON> took up arms on his sister's behalf, and after she had submitted successfully to the ordeal of water, <PERSON><PERSON><PERSON> was compelled to restore her in 858. Still pursuing his purpose, he won the support of his brother, Emperor <PERSON>, by a cession of lands and obtained the consent of the local clergy to the annulment and to his marriage with <PERSON><PERSON><PERSON>, which took place in 862. However, <PERSON> <PERSON> was suspicious of this and sent legates to investigate at the Council of Metz in 863. The Council found in favour of <PERSON><PERSON><PERSON>'s divorce, which led to rumours that the papal legates may have bribed and thus meant that <PERSON> order <PERSON><PERSON><PERSON> to take Teutberga back or face excommunication. With the support of <PERSON> the <PERSON>ld and <PERSON> the German, <PERSON><PERSON><PERSON><PERSON> appealed the annulment to <PERSON>. <PERSON> refused to recognize the annulment and excommunicated <PERSON><PERSON><PERSON> in 866, forcing <PERSON><PERSON><PERSON> to abandon <PERSON><PERSON><PERSON> in favour of <PERSON><PERSON><PERSON><PERSON>.\n", "Title: . Section: . Content: <PERSON><PERSON><PERSON> accepted this begrudgingly for a time, but shortly afterward at the end of 867 <PERSON> <PERSON> died. Thus, <PERSON><PERSON><PERSON> began to seek the permission of the newly appointed Pope <PERSON> to again put <PERSON><PERSON><PERSON><PERSON> aside and marry <PERSON><PERSON><PERSON>, riding to Rome to speak with him on the matter in 869. However, on his way home, <PERSON><PERSON><PERSON> died. Children\n", "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> <PERSON> had some sons and probably three daughters, all of whom were declared illegitimate:\n", "\n", "<PERSON> (c. 855–895), Duke of Alsace (867–885)\n", "<PERSON><PERSON><PERSON> (c. 865–908), who in 883 married <PERSON>, the Viking leader ruling in Frisia, who was murdered in 885\n", "<PERSON><PERSON> (c. 863–925), who married <PERSON><PERSON><PERSON> of Arles (c. 854–895), count of Arles, nephew of <PERSON><PERSON><PERSON><PERSON>. They had two sons, <PERSON> of Italy and <PERSON><PERSON> of Tuscany.\n", "Title: . Section: . Content: After <PERSON><PERSON><PERSON>'s death, between 895 and 898 she married <PERSON><PERSON> of Tuscany (c. 875–915) They had at least three children: <PERSON>, who succeeded his father as count and duke of Lucca and margrave of Tuscany, <PERSON> succeeded his brother in 929, but lost the titles in 931 to his half-brother <PERSON><PERSON> of Tuscany, and <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> (d. 90?) <PERSON><PERSON> (d. c.879)\n", "Number 1:  The Vita Sancti Deicoli states that <PERSON><PERSON><PERSON> was related to <PERSON><PERSON><PERSON>, Count of Nordgau, and the family of Etichonids.\n", "Number 2:  <PERSON><PERSON><PERSON> was the Count of Nordgau.\n", "Number 3:  Nordgau included Strasbourg.\n", "Number 4:  The Vita Sancti Deicoli is a late 10th-century source.\n", "Number 5:  The Vita Sancti Deicoli may not be entirely reliable on this question.\n", "Number 6:  In 855, <PERSON><PERSON> married <PERSON><PERSON><PERSON><PERSON>.\n", "Number 7:  <PERSON><PERSON><PERSON><PERSON> was a Carolingian aristocrat.\n", "Number 8:  <PERSON><PERSON><PERSON><PERSON> was the daughter of <PERSON><PERSON><PERSON> the Elder.\n", "Number 9:  The marriage between <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> was arranged by <PERSON><PERSON> for political reasons.\n", "Number 10:  It is probable that <PERSON><PERSON><PERSON> was already <PERSON><PERSON>'s mistress at this time.\n", "Number 11:  <PERSON><PERSON><PERSON><PERSON> was allegedly not capable of bearing children.\n", "Number 12:  <PERSON><PERSON>'s reign was chiefly occupied by his efforts to obtain an annulment of their marriage.\n", "Number 13:  <PERSON><PERSON>'s relations with his uncles <PERSON> the <PERSON> and <PERSON> the German were influenced by his desire to obtain their support for their annulment.\n", "Number 14:  <PERSON><PERSON><PERSON>'s desire for annulment was arguably prompted by his affection for <PERSON><PERSON><PERSON>.\n", "Number 15:  <PERSON><PERSON><PERSON> put away <PERSON><PERSON><PERSON><PERSON>.\n", "Number 16:  <PERSON><PERSON><PERSON> took up arms on his sister's behalf.\n", "Number 17:  <PERSON><PERSON><PERSON>'s sister submitted successfully to the ordeal of water.\n", "Number 18:  <PERSON><PERSON><PERSON> was compelled to restore <PERSON><PERSON><PERSON>'s sister in 858.\n", "Number 19:  <PERSON><PERSON><PERSON> won the support of his brother, Emperor <PERSON>, by a cession of lands.\n", "Number 20:  <PERSON><PERSON><PERSON> obtained the consent of the local clergy to the annulment and to his marriage with <PERSON><PERSON><PERSON>.\n", "Number 21:  The annulment and marriage with <PERSON><PERSON><PERSON> took place in 862.\n", "Number 22:  <PERSON> I was suspicious of <PERSON><PERSON><PERSON>'s intentions.\n", "Number 23:  <PERSON> I sent legates to investigate at the Council of Metz in 863.\n", "Number 24:  The Council of Metz found in favour of <PERSON><PERSON><PERSON>'s divorce.\n", "Number 25:  R<PERSON><PERSON> suggested that the papal legates may have bribed.\n", "Number 26:  <PERSON> I ordered <PERSON><PERSON><PERSON> to take Teutberga back or face excommunication.\n", "Number 27:  <PERSON> the Bald and <PERSON> the German supported <PERSON><PERSON><PERSON><PERSON>.\n", "Number 28:  <PERSON><PERSON><PERSON><PERSON> appealed the annulment to <PERSON>.\n", "Number 29:  <PERSON> refused to recognize the annulment.\n", "Number 30:  <PERSON> <PERSON> excommunicated <PERSON><PERSON><PERSON> in 866.\n", "Number 31:  <PERSON><PERSON><PERSON> was forced to abandon Waldrada in favour of <PERSON><PERSON><PERSON><PERSON>.\n", "Number 32:  <PERSON><PERSON><PERSON> accepted <PERSON> <PERSON>'s marriage proposal begrudgingly for a time.\n", "Number 33:  <PERSON> died at the end of 867.\n", "Number 34:  <PERSON><PERSON><PERSON> began to seek the permission of Pope <PERSON> to marry <PERSON><PERSON><PERSON>.\n", "Number 35:  <PERSON> was newly appointed.\n", "Number 36:  <PERSON><PERSON><PERSON> rode to Rome to speak with Pope <PERSON> on the matter in 869.\n", "Number 37:  On <PERSON><PERSON><PERSON>'s way home, he died.\n", "Number 38:  <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> had some sons and probably three daughters.\n", "Number 39:  All of the children were declared illegitimate.\n", "Number 40:  <PERSON> was the Duke of Alsace from 855 to 895.\n", "Number 41:  <PERSON><PERSON><PERSON> was the Duke of Alsace from 867 to 885.\n", "Number 42:  <PERSON><PERSON><PERSON> married <PERSON> in 883.\n", "Number 43:  <PERSON> was the Viking leader ruling in Frisia.\n", "Number 44:  <PERSON> was murdered in 885.\n", "Number 45:  <PERSON><PERSON> was the daughter of <PERSON> and <PERSON><PERSON><PERSON>.\n", "Number 46:  <PERSON><PERSON> was born around 863 to 925.\n", "Number 47:  <PERSON><PERSON> married <PERSON><PERSON><PERSON> of Arles.\n", "Number 48:  <PERSON><PERSON><PERSON> of Arles was the count of Arles from 854 to 895.\n", "Number 49:  <PERSON><PERSON><PERSON> of Arles was the nephew of <PERSON><PERSON><PERSON><PERSON>.\n", "Number 50:  <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> of Arles had two sons, <PERSON> of Italy and <PERSON><PERSON> of Tuscany.\n", "Number 51:  <PERSON><PERSON><PERSON> died.\n", "Number 52:  Between 895 and 898, <PERSON><PERSON><PERSON> married <PERSON><PERSON> of Tuscany.\n", "Number 53:  <PERSON><PERSON> of Tuscany lived between 875 and 915.\n", "Number 54:  <PERSON><PERSON><PERSON> and <PERSON><PERSON> of Tuscany had at least three children.\n", "Number 55:  <PERSON> succeeded his father as count and duke of Lucca and margrave of Tuscany.\n", "Number 56:  <PERSON> succeeded his brother in 929.\n", "Number 57:  <PERSON> lost the titles in 931 to his half-brother <PERSON><PERSON> of Tuscany.\n", "Number 58:  <PERSON><PERSON><PERSON><PERSON><PERSON> was one of the children.\n", "Number 59:  <PERSON><PERSON><PERSON><PERSON><PERSON> died in 90.\n", "Number 60:  <PERSON><PERSON> was one of the children.\n", "Number 61:  <PERSON><PERSON> died around 879.\n"]}], "source": ["from lmchunker.modules.dense_x_retrieval import dense_x_retrieval\n", "import json\n", "with open('data/example1.json', 'r', encoding='utf-8') as file:  \n", "    examples = json.load(file)\n", "language='en' # At present, the model of this method is only applicable to English texts\n", "text=examples[0][language] # Text that needs to be segmented\n", "title=''\n", "section=''\n", "target_size=256  # This model can only accept a maximum length of 512, but longer texts tend to make it difficult for the model to extract effective information.\n", "chunks=dense_x_retrieval(tokenizer,model,text,title,section,target_size)\n", "i=1\n", "for chunk in chunks:\n", "    print(f'Number {i}: ', chunk)\n", "    i+=1"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}