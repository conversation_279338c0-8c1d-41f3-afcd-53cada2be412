{"cells": [{"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["151.86104658606624"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# Chinese chunk length\n", "with open('chunking/db_qa_baichuan_nodie_dynamic_00', 'r', encoding='utf-8') as file:  \n", " \n", "    content = file.read()  \n", "sentences_lists=content.split('\\n')\n", "len_sents=0\n", "len_lists=0\n", "for sentence in sentences_lists:\n", "    len_sents+=len(sentence)  #In Chinese, len(sentence) is used here.\n", "    len_lists+=1\n", "len_sents/len_lists"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["# Dynamic combination of chunks\n", "import json\n", "target_size=193\n", "merged_paragraphs = []  \n", "current_paragraph = \"\" \n", "filename1='chunking/db_qa_qwen15B_nodie_dynamic_00_merge.json' \n", "filename='chunking/db_qa_qwen15B_nodie_dynamic_00_merge'\n", "with open('chunking/db_qa_qwen15B_nodie_dynamic_00', 'r', encoding='utf-8') as file:   \n", "    content = file.read()  \n", "sentences_lists=content.split('\\n')\n", "for paragraph in sentences_lists:  \n", "    # Check if adding a new paragraph to the current paragraph exceeds the target size\n", "    if len(current_paragraph) + len(paragraph) <= target_size:  \n", "        current_paragraph +=paragraph  \n", "    else:  \n", "        merged_paragraphs.append(current_paragraph)  # Add the current merged paragraph to the result list\n", "        current_paragraph = paragraph  # Reset the current paragraph to a new paragraph  \n", "if current_paragraph:  \n", "    merged_paragraphs.append(current_paragraph)  \n", "with open(filename, 'w', encoding='utf-8') as file:  \n", "    file.write('\\n'.join(merged_paragraphs))\n", "with open(filename1, 'w') as file:\n", "    json.dump(merged_paragraphs, file)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}