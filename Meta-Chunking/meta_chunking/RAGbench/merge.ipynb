{"cells": [{"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["97.58831003811945"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["# English paragraph length\n", "import json\n", "with open('chunking/CUAD_baichuan_nodie_dynamic_00.json', 'r', encoding='utf-8') as file:  \n", "    qa_data = json.load(file)\n", "len_sents=0\n", "len_lists=0\n", "for sentence in qa_data:\n", "    len_sents+=len(sentence.split())\n", "    len_lists+=1\n", "len_sents/len_lists"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["# Dynamic combination of English text\n", "import json\n", "target_size=69\n", "merged_paragraphs = []  \n", "current_paragraph = \"\" \n", "filename='chunking/techqa_baichuan_nodie_dynamic_00_merge.json' \n", "with open('chunking/techqa_baichuan_nodie_dynamic_00.json', 'r', encoding='utf-8') as file:  \n", "    paragraphs = json.load(file)\n", "for paragraph in paragraphs:  \n", "    if len(current_paragraph.split()) + len(paragraph.split()) <= target_size:  \n", "        current_paragraph +=' '+paragraph  \n", "    else:  \n", "        merged_paragraphs.append(current_paragraph) \n", "        current_paragraph = paragraph  \n", "if current_paragraph:  \n", "    merged_paragraphs.append(current_paragraph)  \n", "with open(filename, 'w') as file:\n", "    json.dump(merged_paragraphs, file)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}