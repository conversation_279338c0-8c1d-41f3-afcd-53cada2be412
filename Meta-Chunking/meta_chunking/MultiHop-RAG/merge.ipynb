{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["65.06426994638915"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# English paragraph length\n", "with open('data/corpus/corpus_nodie_internlm2_18B_00', 'r', encoding='utf-8') as file:   \n", "    content = file.read()  \n", "sentences_lists=content.split('\\n')\n", "len_sents=0\n", "len_lists=0\n", "for sentence in sentences_lists:\n", "    len_sents+=len(sentence.split(' '))  #中文时这里用len(sentence)\n", "    len_lists+=1\n", "len_sents/len_lists"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# Dynamic combination of English text\n", "import json\n", "target_size=90\n", "merged_paragraphs = []  \n", "current_paragraph = \"\" \n", "filename='data/corpus/corpus_nodie_glm4_00_merge.json' \n", "filename1='data/corpus/corpus_nodie_glm4_00_merge'\n", "with open('data/corpus/corpus_nodie_glm4_00', 'r', encoding='utf-8') as file:  \n", "\n", "    content = file.read()  \n", "sentences_lists=content.split('\\n')\n", "for paragraph in sentences_lists:  \n", "    if len(current_paragraph.split()) + len(paragraph.split()) <= target_size:  \n", "        current_paragraph +=' '+paragraph  \n", "    else:  \n", "        merged_paragraphs.append(current_paragraph)  \n", "        current_paragraph = paragraph   \n", "if current_paragraph:  \n", "    merged_paragraphs.append(current_paragraph)  \n", "with open(filename1, 'w', encoding='utf-8') as file:  \n", "    file.write('\\n'.join(merged_paragraphs))\n", "with open(filename, 'w') as file:\n", "    json.dump(merged_paragraphs, file)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["86.36662468513853"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# English paragraph length\n", "import json\n", "with open('chunk_others/corpus_ppl_qwen15B_die_0.json', 'r', encoding='utf-8') as file:  \n", "    qa_data = json.load(file)\n", "len_sents=0\n", "len_lists=0\n", "for sentence in qa_data:\n", "    len_sents+=len(sentence.split())\n", "    len_lists+=1\n", "len_sents/len_lists"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# Dynamic combination of English text\n", "import json\n", "target_size=91\n", "merged_paragraphs = []  \n", "current_paragraph = \"\" \n", "filename='chunk_others/corpus_prob_baichuan_merge.json' \n", "with open('chunk_others/corpus_prob_baichuan.json', 'r', encoding='utf-8') as file:  \n", "    paragraphs = json.load(file)\n", "for paragraph in paragraphs:  \n", "    if len(current_paragraph.split()) + len(paragraph.split()) <= target_size:  \n", "        current_paragraph +=' '+paragraph  \n", "    else:  \n", "        merged_paragraphs.append(current_paragraph)  \n", "        current_paragraph = paragraph  \n", "if current_paragraph:  \n", "    merged_paragraphs.append(current_paragraph)  \n", "with open(filename, 'w') as file:\n", "    json.dump(merged_paragraphs, file)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Store the dataset in a style that is convenient for chunking\n", "import json   \n", "i=1\n", "with open('data/corpus/corpus.txt', 'r', encoding='utf-8') as file:  \n", "    content = file.read()  \n", "sentences_lists=content.split('\\n')\n", "for text in sentences_lists:\n", "    zzpath='tmp/corpus/'+str(i)+'.txt'\n", "    with open(zzpath, 'w') as file:  \n", "        file.write(text)\n", "    i+=1"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON><PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}